#!/usr/bin/env python3
"""
Test wydajności parsowania sygnałów - sprawdza jak szybko system parsuje duże ilości sygnałów.
"""

import time
import sys
import os
from typing import List, Dict, Any

# Dodaj ście<PERSON> do głównego skryptu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_test_signals(count: int = 1000) -> List[str]:
    """Generuje listę testowych sygnałów w różnych formatach."""
    
    formats = [
        # Format podstawowy
        """BUY BTCUSDT
Entry - {entry}
TP - {tp}
SL - {sl}
Timeframe: {tf}""",

        # Format LONG/SHORT
        """LONG {pair}
Entry: {entry}
Take Profit: {tp}
Stop Loss: {sl}
Duration: {tf}""",

        # Format z wieloma TP
        """SHORT {pair}
Entry = {entry}
TP1 = {tp}
TP2 = {tp2}
TP3 = {tp3}
SL = {sl}
TF: {tf}""",

        # Format z emoji
        """💎 SELL {pair} 💎
📈 Entry @ {entry}
🎯 Target: {tp}
⚡ Stop: {sl}
⏰ Time: {tf}""",

        # Format z przecinkami
        """BUY {pair}
Entry: {entry_comma}
TP: {tp_comma}
SL: {sl_comma}
Timeframe: {tf}""",

        # Format prosty bez timeframe
        """BUY {pair}
Entry - {entry}
TP - {tp}
SL - {sl}"""
    ]
    
    pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'BNBUSDT', 'XRPUSDT', 'DOGEUSDT', 'LINKUSDT']
    
    signals = []
    
    for i in range(count):
        format_template = formats[i % len(formats)]
        pair = pairs[i % len(pairs)]
        
        # Generuj losowe wartości
        entry = 100 + (i % 1000)
        tp = entry + 5 + (i % 10)
        sl = entry - 5 - (i % 5)
        tf = 60 + (i % 180)
        
        # Dla formatów z wieloma TP
        tp2 = tp + 2
        tp3 = tp + 4
        
        # Dla formatów z przecinkami
        entry_comma = str(entry).replace('.', ',')
        tp_comma = str(tp).replace('.', ',')
        sl_comma = str(sl).replace('.', ',')
        
        signal = format_template.format(
            pair=pair,
            entry=entry,
            tp=tp,
            tp2=tp2,
            tp3=tp3,
            sl=sl,
            tf=tf,
            entry_comma=entry_comma,
            tp_comma=tp_comma,
            sl_comma=sl_comma
        )
        
        signals.append(signal)
    
    return signals

def test_parsing_performance():
    """Test wydajności parsowania sygnałów."""
    
    try:
        from discord_bybit_signal_monitor import parse_signal_advanced
    except ImportError:
        print("❌ Nie można zaimportować funkcji parse_signal_advanced")
        return
    
    print("🚀 Test wydajności parsowania sygnałów\n")
    
    # Testy z różną liczbą sygnałów
    test_counts = [100, 500, 1000, 2000]
    
    for count in test_counts:
        print(f"📊 Test z {count} sygnałami:")
        
        # Generuj sygnały
        signals = generate_test_signals(count)
        
        # Zmierz czas parsowania
        start_time = time.time()
        
        parsed_count = 0
        warning_count = 0
        error_count = 0
        
        for signal in signals:
            try:
                result = parse_signal_advanced(signal)
                if result:
                    parsed_count += 1
                    if result.get('warnings'):
                        warning_count += len(result['warnings'])
                else:
                    error_count += 1
            except Exception as e:
                error_count += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Wyniki
        success_rate = (parsed_count / count) * 100
        signals_per_second = count / duration if duration > 0 else 0
        
        print(f"   ⏱️  Czas: {duration:.3f}s")
        print(f"   🎯 Sparsowane: {parsed_count}/{count} ({success_rate:.1f}%)")
        print(f"   ⚠️  Ostrzeżenia: {warning_count}")
        print(f"   ❌ Błędy: {error_count}")
        print(f"   🚄 Wydajność: {signals_per_second:.1f} sygnałów/s")
        print()

def test_pattern_efficiency():
    """Test efektywności różnych wzorców regex."""
    
    try:
        from discord_bybit_signal_monitor import SIGNAL_PATTERNS
    except ImportError:
        print("❌ Nie można zaimportować SIGNAL_PATTERNS")
        return
    
    print("🔍 Test efektywności wzorców regex\n")
    
    # Generuj różne typy sygnałów
    test_signals = {
        'basic': """BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60""",
        
        'multi_tp': """SHORT BNBUSDT
Entry = 300.00
TP1 = 290.00
TP2 = 285.00
TP3 = 280.00
SL = 310.00
TF: 180""",
        
        'emoji': """💎 SELL XRPUSDT 💎
📈 Entry @ 0.6500
🎯 Target: 0.6200
⚡ Stop: 0.6800
⏰ Time: 45""",
        
        'simple': """BUY LINKUSDT
Entry - 15.50
TP - 16.00
SL - 15.00"""
    }
    
    for signal_type, signal_text in test_signals.items():
        print(f"📝 Test dla typu: {signal_type}")
        
        for i, pattern in enumerate(SIGNAL_PATTERNS):
            start_time = time.time()
            
            # Test 1000 razy ten sam pattern
            matches = 0
            for _ in range(1000):
                match = pattern.search(signal_text)
                if match:
                    matches += 1
            
            end_time = time.time()
            duration = end_time - start_time
            
            pattern_name = f"Pattern {i+1}"
            if matches > 0:
                print(f"   ✅ {pattern_name}: {duration:.4f}s ({matches} dopasowań)")
            else:
                print(f"   ❌ {pattern_name}: {duration:.4f}s (brak dopasowań)")
        
        print()

def main():
    """Główna funkcja testowa."""
    print("🧪 Test wydajności systemu parsowania sygnałów Discord\n")
    
    test_parsing_performance()
    test_pattern_efficiency()
    
    print("🎉 Testy wydajności zakończone!")

if __name__ == '__main__':
    main()
