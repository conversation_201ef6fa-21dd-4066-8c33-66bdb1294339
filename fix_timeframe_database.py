#!/usr/bin/env python3
"""
Napraw timeframe w bazie danych - ustaw wszystkie sygnały na 48h ważności.
"""

import sqlite3
import os
from dotenv import load_dotenv

# Ładowanie zmiennych środowiskowych
load_dotenv()
SIGNAL_VALIDITY_HOURS = int(os.getenv('SIGNAL_VALIDITY_HOURS', 48))

def fix_timeframe_database():
    """Napraw timeframe w bazie danych."""
    
    conn = sqlite3.connect('signals.db')
    cursor = conn.cursor()
    
    # Sprawdź aktualny stan
    cursor.execute('SELECT COUNT(*) FROM signals WHERE timeframe_min = 1')
    signals_with_1min = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM signals WHERE timeframe_min != ?', (SIGNAL_VALIDITY_HOURS * 60,))
    signals_to_fix = cursor.fetchone()[0]
    
    print(f"Sygnały z TF 1 minuta: {signals_with_1min}")
    print(f"Sygnały do naprawy: {signals_to_fix}")
    print(f"Docelowy timeframe: {SIGNAL_VALIDITY_HOURS * 60} minut (48h)")
    
    if signals_to_fix == 0:
        print("✅ Wszystkie sygnały mają już poprawny timeframe!")
        conn.close()
        return
    
    # Zapytaj o potwierdzenie
    response = input(f"\nCzy chcesz naprawić {signals_to_fix} sygnałów? (y/N): ")
    if response.lower() != 'y':
        print("Anulowano.")
        conn.close()
        return
    
    # Utwórz backup przed zmianami
    print("Tworzenie backupu...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS signals_backup AS 
        SELECT * FROM signals
    ''')
    
    # Napraw timeframe dla wszystkich sygnałów
    correct_timeframe = SIGNAL_VALIDITY_HOURS * 60  # 48h = 2880 minut
    
    cursor.execute('''
        UPDATE signals 
        SET timeframe_min = ? 
        WHERE timeframe_min != ?
    ''', (correct_timeframe, correct_timeframe))
    
    updated_count = cursor.rowcount
    conn.commit()
    
    print(f"✅ Zaktualizowano {updated_count} sygnałów")
    
    # Sprawdź wynik
    cursor.execute('SELECT DISTINCT timeframe_min, COUNT(*) FROM signals GROUP BY timeframe_min')
    tf_stats = cursor.fetchall()
    
    print("\nNowe statystyki timeframe:")
    for tf, count in tf_stats:
        print(f"  {tf} minut: {count} sygnałów")
    
    # Sprawdź otwarte sygnały
    cursor.execute('SELECT COUNT(*) FROM signals WHERE status = "open"')
    open_count = cursor.fetchone()[0]
    print(f"\nOtwarte sygnały: {open_count}")
    
    if open_count > 0:
        print("ℹ️  Otwarte sygnały będą teraz ważne przez 48h od momentu utworzenia")
    
    conn.close()
    print("\n✅ Naprawa zakończona pomyślnie!")

if __name__ == '__main__':
    fix_timeframe_database()
