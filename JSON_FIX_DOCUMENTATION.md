# Dokumentacja naprawy błędów JSON (NaN/Infinity)

**Data:** 2025-06-14  
**Problem:** Dashboard wyś<PERSON><PERSON><PERSON><PERSON> błędy JSON z powodu wartości `NaN` i `Infinity` w danych

## 🐛 Zidentyfikowane problemy

### Błędy w konsoli przeglądarki:
```
SyntaxError: Unexpected token 'N', ..."avg_pnl": NaN, is not valid JSON
SyntaxError: Unexpected token 'I', ..."_factor": Infinity, is not valid JSON
TypeError: Cannot read properties of undefined (reading 'y')
```

### Źródła problemu:

1. **dashboard.py linia 180**: `profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')`
2. **dashboard.py linie 183-184**: `avg_win` i `avg_loss` mogły generować `NaN` przez pandas `.mean()`
3. **dashboard.py linia 164**: `sharpe_ratio` mógł generować `NaN` jeśli `pnl_series.std()` było 0
4. **Pandas DataFrame**: `.to_dict('records')` mogło zawierać `NaN` z bazy danych

## 🔧 Implementowane rozwiązania

### 1. Funkcja sanityzacji JSON

Dodano funkcję `sanitize_for_json()` która:
- Zastępuje `NaN` → `0.0`
- Zastępuje `+Infinity` → `999999.0`
- Zastępuje `-Infinity` → `-999999.0`
- Obsługuje zagnieżdżone struktury (dict, list)
- Konwertuje typy NumPy do standardowych typów Python

```python
def sanitize_for_json(obj):
    """Sanityzuje obiekt do bezpiecznego formatu JSON."""
    if isinstance(obj, dict):
        return {key: sanitize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, float):
        if math.isnan(obj):
            return 0.0
        elif math.isinf(obj):
            return 999999.0 if obj > 0 else -999999.0
        else:
            return obj
    # ... więcej przypadków
```

### 2. Bezpieczne obliczenia w `_calculate_advanced_metrics()`

**Przed:**
```python
profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
avg_win = pnl_series[pnl_series > 0].mean() if (pnl_series > 0).any() else 0
```

**Po:**
```python
if total_losses > 0:
    profit_factor = total_wins / total_losses
    if pd.isna(profit_factor) or math.isinf(profit_factor):
        profit_factor = 999999.0
else:
    profit_factor = 999999.0 if total_wins > 0 else 0.0

if (pnl_series > 0).any():
    avg_win = pnl_series[pnl_series > 0].mean()
    if pd.isna(avg_win):
        avg_win = 0.0
else:
    avg_win = 0.0
```

### 3. Sanityzacja wszystkich API endpoint'ów

Wszystkie endpoint'y teraz używają sanityzacji:
- `/api/signals` → `sanitize_for_json(signals)`
- `/api/statistics` → `sanitize_for_json(stats)`
- `/api/performance-metrics` → `sanitize_for_json(performance_metrics)`
- `/api/heatmap-data` → `sanitize_for_json(heatmap_data)`
- `/api/pnl-chart` → `sanitize_for_json(data)`
- `/api/pnl-distribution` → `sanitize_for_json(data)`

### 4. Sanityzacja WebSocket komunikacji

```python
def broadcast_new_signal(signal_data):
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('new_signal', sanitized_data)

def broadcast_signal_update(signal_data):
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('signal_update', sanitized_data)
```

### 5. Bezpieczne obliczenia w `get_pnl_distribution()`

Dodano walidację dla bin_edges i percentage:
```python
percentage = (count / len(pnl_values)) * 100 if len(pnl_values) > 0 else 0.0
if pd.isna(percentage):
    percentage = 0.0

distribution.append({
    'range_start': float(bin_edges[i]) if not pd.isna(bin_edges[i]) else 0.0,
    'range_end': float(bin_edges[i + 1]) if not pd.isna(bin_edges[i + 1]) else 0.0,
    'count': int(count),
    'percentage': percentage
})
```

## 🧪 Testowanie

Stworzono `test_json_fix.py` który testuje:
- Podstawowe przypadki sanityzacji
- Problematyczne wartości (NaN, Infinity)
- Typy NumPy
- Zagnieżdżone struktury
- Scenariusze z prawdziwymi danymi

**Wyniki testów:** ✅ 14/14 passed

## 📊 Rezultaty

### Przed naprawą:
```javascript
// Błędy w konsoli
SyntaxError: Unexpected token 'N', ..."avg_pnl": NaN,
SyntaxError: Unexpected token 'I', ..."_factor": Infinity,
```

### Po naprawie:
```json
{
  "profit_factor": 999999.0,
  "avg_win": 0.0034268519497658695,
  "sharpe_ratio": 1.2912135085554677,
  "max_drawdown": 0.0
}
```

## 🔍 Weryfikacja

1. **API endpoint'y:** ✅ Wszystkie zwracają prawidłowy JSON
2. **Dashboard:** ✅ Ładuje się bez błędów
3. **WebSocket:** ✅ Komunikacja działa poprawnie
4. **Wykresy:** ✅ Renderują się bez błędów

## 📝 Uwagi implementacyjne

- Wartość `999999.0` dla Infinity jest wystarczająco duża dla celów dashboardu
- Wartość `0.0` dla NaN jest bezpieczna dla większości metryk
- Funkcja sanityzacji jest rekurencyjna i obsługuje złożone struktury
- Wszystkie obliczenia matematyczne mają teraz walidację NaN/Infinity

## 🚀 Następne kroki

1. Monitorowanie dashboardu pod kątem nowych błędów JSON
2. Rozważenie dodania logowania dla przypadków sanityzacji
3. Możliwe dodanie konfigurowalnych wartości zastępczych dla NaN/Infinity
