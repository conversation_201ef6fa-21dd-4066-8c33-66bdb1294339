#!/usr/bin/env python3
"""
Test konfiguracji obsługi botów
Sprawdza czy nowe funkcje działają poprawnie.
"""

import os
import sys
from dotenv import load_dotenv

# Załaduj zmienne środowiskowe
load_dotenv()

def test_bot_configuration():
    """Test konfiguracji obsługi botów."""
    print("🧪 Test konfiguracji obsługi botów")
    print("=" * 50)
    
    # Import funkcji z głównego modułu
    try:
        from discord_bybit_signal_monitor import (
            ALLOW_BOT_MESSAGES, 
            BOT_WHITELIST, 
            SIGNAL_VALIDITY_HOURS,
            is_bot_allowed
        )
        print("✅ Import funkcji - OK")
    except ImportError as e:
        print(f"❌ Błąd importu: {e}")
        return False
    
    # Test konfiguracji
    print(f"\n📋 Aktualna konfiguracja:")
    print(f"   ALLOW_BOT_MESSAGES: {ALLOW_BOT_MESSAGES}")
    print(f"   BOT_WHITELIST: {BOT_WHITELIST}")
    print(f"   SIGNAL_VALIDITY_HOURS: {SIGNAL_VALIDITY_HOURS}")
    
    # Test funkcji is_bot_allowed
    print(f"\n🤖 Test funkcji is_bot_allowed:")
    
    # Test case 1: Boty wyłączone
    if not ALLOW_BOT_MESSAGES:
        result = is_bot_allowed(123456789)
        print(f"   Bot 123456789 (boty wyłączone): {result} ✅" if not result else f"   Bot 123456789 (boty wyłączone): {result} ❌")
    
    # Test case 2: Boty włączone, whitelist pusty
    os.environ['ALLOW_BOT_MESSAGES'] = 'true'
    os.environ['BOT_WHITELIST'] = ''
    
    # Reload konfiguracji
    from importlib import reload
    import discord_bybit_signal_monitor
    reload(discord_bybit_signal_monitor)
    
    from discord_bybit_signal_monitor import is_bot_allowed as is_bot_allowed_test
    
    result = is_bot_allowed_test(123456789)
    print(f"   Bot 123456789 (whitelist pusty): {result} ✅" if result else f"   Bot 123456789 (whitelist pusty): {result} ❌")
    
    # Test case 3: Boty włączone, bot na whiteliście
    os.environ['BOT_WHITELIST'] = '123456789,987654321'
    reload(discord_bybit_signal_monitor)
    from discord_bybit_signal_monitor import is_bot_allowed as is_bot_allowed_test2
    
    result_allowed = is_bot_allowed_test2(123456789)
    result_not_allowed = is_bot_allowed_test2(555555555)
    
    print(f"   Bot 123456789 (na whiteliście): {result_allowed} ✅" if result_allowed else f"   Bot 123456789 (na whiteliście): {result_allowed} ❌")
    print(f"   Bot 555555555 (nie na whiteliście): {result_not_allowed} ✅" if not result_not_allowed else f"   Bot 555555555 (nie na whiteliście): {result_not_allowed} ❌")
    
    print(f"\n✅ Test zakończony pomyślnie!")
    return True

def test_signal_validity():
    """Test konfiguracji czasu ważności sygnałów."""
    print("\n⏰ Test czasu ważności sygnałów")
    print("=" * 50)
    
    from datetime import datetime, timedelta, timezone
    
    try:
        from discord_bybit_signal_monitor import SIGNAL_VALIDITY_HOURS
        print(f"   Czas ważności sygnałów: {SIGNAL_VALIDITY_HOURS} godzin")
        
        # Symulacja sprawdzenia wieku wiadomości
        now = datetime.now(timezone.utc)
        old_message_time = now - timedelta(hours=SIGNAL_VALIDITY_HOURS + 1)
        recent_message_time = now - timedelta(hours=1)
        
        old_age = now - old_message_time
        recent_age = now - recent_message_time
        
        print(f"   Stara wiadomość ({old_age}): {'Odrzucona' if old_age > timedelta(hours=SIGNAL_VALIDITY_HOURS) else 'Zaakceptowana'} ✅")
        print(f"   Nowa wiadomość ({recent_age}): {'Odrzucona' if recent_age > timedelta(hours=SIGNAL_VALIDITY_HOURS) else 'Zaakceptowana'} ✅")
        
        return True
    except Exception as e:
        print(f"❌ Błąd testu: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Uruchamianie testów konfiguracji...")
    
    success = True
    success &= test_bot_configuration()
    success &= test_signal_validity()
    
    if success:
        print("\n🎉 Wszystkie testy przeszły pomyślnie!")
        sys.exit(0)
    else:
        print("\n💥 Niektóre testy nie powiodły się!")
        sys.exit(1)
