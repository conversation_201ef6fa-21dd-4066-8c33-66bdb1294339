#!/usr/bin/env python3
"""
Import historycznych sygnałów z Discord

Skrypt do pobierania i analizowania historycznych wiadomości z kanału Discord
w celu zaimportowania starszych sygnałów do bazy danych.

Autor: AI Assistant
Data: 2025-06-14
"""

import os
import re
import sqlite3
import asyncio
import discord
from datetime import datetime, timezone
from dotenv import load_dotenv

# Ładowanie konfiguracji
load_dotenv()
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
CHANNEL_ID = int(os.getenv('DISCORD_CHANNEL_ID'))
DB_PATH = os.getenv('DB_PATH', 'signals.db')

# Import zaawansowanego parsera z głównego skryptu
try:
    from discord_bybit_signal_monitor import parse_signal_advanced
    ADVANCED_PARSER_AVAILABLE = True
    print("✅ Załadowano zaawansowany parser sygnałów")
except ImportError:
    ADVANCED_PARSER_AVAILABLE = False
    print("⚠️  Nie można zała<PERSON><PERSON>ć zaawansowanego parsera - używam podstawowego")

    # Fallback - podstawowy regex (stary format)
    signal_pattern = re.compile(
        r"(?P<side>BUY|SELL)\s+(?P<pair>[A-Z0-9]+)\.?P?\s*"
        r".*?"
        r"Entry\s*[-:]\s*(?P<entry>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"TP\s*[-:]\s*(?P<tp>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"SL\s*[-:]\s*(?P<sl>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"Time(?:frame)?[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

class DiscordHistoryImporter:
    """Klasa do importu historycznych sygnałów z Discord."""

    def __init__(self):
        self.client = discord.Client(intents=discord.Intents.default())
        self.db_path = DB_PATH
        self.imported_count = 0
        self.skipped_count = 0
        self.error_count = 0

        # Event handlers
        self.client.event(self.on_ready)

    async def on_ready(self):
        """Callback po połączeniu z Discord."""
        print(f'🔗 Połączono z Discord jako {self.client.user}')

        try:
            channel = self.client.get_channel(CHANNEL_ID)
            if not channel:
                print(f'❌ Nie znaleziono kanału o ID: {CHANNEL_ID}')
                return

            print(f'📥 Rozpoczynam import z kanału: #{channel.name}')
            await self.import_channel_history(channel)

        except Exception as e:
            print(f'❌ Błąd podczas importu: {e}')
        finally:
            await self.client.close()

    async def import_channel_history(self, channel, limit=1000):
        """Importuj historię wiadomości z kanału."""
        print(f'🔍 Pobieranie ostatnich {limit} wiadomości...')

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Pobierz istniejące message_id aby uniknąć duplikatów
        cursor.execute("SELECT message_id FROM signals")
        existing_ids = {row[0] for row in cursor.fetchall()}

        message_count = 0
        async for message in channel.history(limit=limit):
            message_count += 1

            # Sprawdź czy wiadomość jest od bota i czy boty są dozwolone
            if message.author.bot:
                # Import z głównego skryptu funkcji sprawdzania botów
                try:
                    from discord_bybit_signal_monitor import is_bot_allowed
                    if not is_bot_allowed(message.author.id):
                        self.skipped_count += 1
                        continue
                    else:
                        print(f'📋 Przetwarzanie wiadomości od dozwolonego bota: {message.author.name} (ID: {message.author.id})')
                except ImportError:
                    # Fallback - pomiń wszystkie boty jeśli nie można zaimportować funkcji
                    self.skipped_count += 1
                    continue

            # Pomiń jeśli już istnieje
            if str(message.id) in existing_ids:
                self.skipped_count += 1
                continue

            # Spróbuj sparsować sygnał
            signal_data = self.parse_signal(message)
            if signal_data:
                try:
                    self.save_signal(cursor, message, signal_data)
                    self.imported_count += 1
                    print(f'✅ Zaimportowano sygnał {message.id}: {signal_data["side"]} {signal_data["pair"]}')
                except Exception as e:
                    print(f'❌ Błąd zapisu sygnału {message.id}: {e}')
                    self.error_count += 1

        conn.commit()
        conn.close()

        print(f'\n📊 Podsumowanie importu:')
        print(f'   Przeanalizowano wiadomości: {message_count}')
        print(f'   Zaimportowano sygnałów: {self.imported_count}')
        print(f'   Pominięto (duplikaty): {self.skipped_count}')
        print(f'   Błędy: {self.error_count}')

    def parse_signal(self, message):
        """Parsuj wiadomość w poszukiwaniu sygnału używając zaawansowanego parsera."""

        if ADVANCED_PARSER_AVAILABLE:
            # Użyj zaawansowanego parsera
            signal_data = parse_signal_advanced(message.content)
            if signal_data:
                # Usuń dodatkowe pola które nie są potrzebne w bazie danych
                result = {
                    'pair': signal_data['pair'],
                    'side': signal_data['side'],
                    'entry': signal_data['entry'],
                    'tp': signal_data['tp'],
                    'sl': signal_data['sl'],
                    'timeframe_min': signal_data['timeframe_min']
                }

                # Wyloguj ostrzeżenia jeśli istnieją
                if signal_data.get('warnings'):
                    print(f'⚠️  Ostrzeżenia walidacji dla wiadomości {message.id}:')
                    for warning in signal_data['warnings']:
                        print(f'     - {warning}')

                return result
            return None
        else:
            # Fallback - użyj podstawowego parsera
            match = signal_pattern.search(message.content)
            if not match:
                return None

            try:
                data = match.groupdict()

                # Normalizacja nazwy pary (stary sposób)
                pair = data['pair'].upper().replace('.P','')
                if not pair.endswith('USDT'):
                    pair += 'USDT'

                return {
                    'pair': pair,
                    'side': data['side'].upper(),
                    'entry': float(data['entry']),
                    'tp': float(data['tp']),
                    'sl': float(data['sl']),
                    'timeframe_min': int(data['tf'])
                }
            except (ValueError, KeyError) as e:
                print(f'⚠️  Błąd parsowania sygnału w wiadomości {message.id}: {e}')
                return None

    def save_signal(self, cursor, message, signal_data):
        """Zapisz sygnał do bazy danych."""
        # Konwertuj timestamp Discord na UTC
        timestamp = message.created_at.replace(tzinfo=timezone.utc)

        cursor.execute("""
            INSERT INTO signals (
                message_id, pair, side, entry, tp, sl,
                timestamp, timeframe_min, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            str(message.id),
            signal_data['pair'],
            signal_data['side'],
            signal_data['entry'],
            signal_data['tp'],
            signal_data['sl'],
            timestamp,
            signal_data['timeframe_min'],
            'historical'  # Oznacz jako historyczny
        ))

    async def run(self):
        """Uruchom import."""
        await self.client.start(DISCORD_TOKEN)

async def main():
    """Główna funkcja."""
    print("📚 Discord History Importer")
    print("=" * 50)

    if not DISCORD_TOKEN:
        print("❌ Brak tokena Discord w pliku .env")
        return

    if not CHANNEL_ID:
        print("❌ Brak ID kanału Discord w pliku .env")
        return

    # Sprawdź czy baza danych istnieje
    if not os.path.exists(DB_PATH):
        print(f"❌ Baza danych nie istnieje: {DB_PATH}")
        print("Uruchom najpierw główny bot aby utworzyć bazę danych.")
        return

    # Zapytaj użytkownika o limit
    try:
        limit_input = input("Ile wiadomości pobrać? (domyślnie 1000, max 10000): ").strip()
        limit = int(limit_input) if limit_input else 1000
        limit = min(limit, 10000)  # Ograniczenie bezpieczeństwa
    except ValueError:
        limit = 1000

    print(f"🚀 Rozpoczynam import {limit} wiadomości...")

    importer = DiscordHistoryImporter()

    # Nadpisz limit w metodzie
    original_method = importer.import_channel_history
    async def import_with_limit(channel):
        return await original_method(channel, limit)
    importer.import_channel_history = import_with_limit

    try:
        await importer.run()
    except KeyboardInterrupt:
        print("\n⏹️  Import przerwany przez użytkownika")
    except Exception as e:
        print(f"\n❌ Błąd podczas importu: {e}")

if __name__ == '__main__':
    asyncio.run(main())
