# Przewodnik po zaawansowanym parsowaniu sygnałów

## Przegląd

System parsowania sygnałów Discord został znacznie ulepszony i teraz obsługuje szeroką gamę formatów sygnałów tradingowych. System wykorzystuje 4 różne wzorce regex do rozpoznawania sygnałów w różnych formatach.

## Wzorce regex

### 1. MULTI_TP_PATTERN
Obsługuje sygnały z wielokrotnymi poziomami Take Profit (TP1, TP2, TP3).

**Przykład:**
```
SHORT BNBUSDT
Entry = 300.00
TP1 = 290.00
TP2 = 285.00
TP3 = 280.00
SL = 310.00
TF: 180
```

### 2. FORMATTED_PATTERN
Obsługuje sygnały z emoji i specjalnym formatowaniem.

**Przykład:**
```
💎 SELL XRPUSDT 💎
📈 Entry @ 0.6500
🎯 Target: 0.6200
⚡ Stop: 0.6800
⏰ Time: 45
```

### 3. MAIN_PATTERN
Główny wzorzec dla standardowych sygnałów.

**Przykład:**
```
BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60
```

### 4. SIMPLE_PATTERN
Wzorzec dla prostych sygnałów bez timeframe.

**Przykład:**
```
BUY LINKUSDT
Entry - 15.50
TP - 16.00
SL - 15.00
```

## Funkcje normalizacji

### normalize_number(value: str) -> float
Normalizuje różne formaty liczb:
- Zamienia przecinki na kropki (0,085 → 0.085)
- Usuwa niepotrzebne znaki
- Obsługuje różne separatory dziesiętne

### normalize_pair(pair: str) -> str
Normalizuje nazwy par tradingowych:
- Usuwa sufiks .P (ETH.P → ETH)
- Usuwa sufiks PERP
- Dodaje USDT jeśli brak quote currency
- Obsługuje różne quote currencies: USDT, USDC, BTC, ETH, BNB

### normalize_side(side: str) -> str
Normalizuje kierunek pozycji:
- LONG → BUY
- SHORT → SELL
- Zachowuje BUY/SELL

## Walidacja sygnałów

### validate_signal_logic(side, entry, tp, sl) -> list
Sprawdza logikę sygnału i zwraca ostrzeżenia:

**Dla pozycji BUY:**
- TP powinno być wyższe niż Entry
- SL powinno być niższe niż Entry

**Dla pozycji SELL:**
- TP powinno być niższe niż Entry
- SL powinno być wyższe niż Entry

**Dodatkowe sprawdzenia:**
- Czy różnice nie są zbyt małe (< 0.1%)
- Risk/Reward ratio (ostrzeżenie jeśli < 0.5)

## Obsługiwane formaty

### Kierunki pozycji
- `BUY`, `SELL` - standardowe
- `LONG`, `SHORT` - alternatywne (konwertowane na BUY/SELL)

### Pary tradingowe
- `BTCUSDT` - pełna nazwa
- `BTC` - automatycznie dodaje USDT
- `ETH.P` - usuwa .P i dodaje USDT
- `SOLPERP` - usuwa PERP i dodaje USDT

### Słowa kluczowe Entry
- `Entry`, `Price`, `@`

### Słowa kluczowe TP
- `TP`, `Take Profit`, `Target`
- `TP1`, `TP2`, `TP3` - dla wielokrotnych poziomów

### Słowa kluczowe SL
- `SL`, `Stop Loss`, `Stop`

### Słowa kluczowe Timeframe
- `Timeframe`, `Time`, `TF`, `Duration`

### Separatory
- `-`, `:`, `=`, spacje

### Formaty liczb
- `45000` - liczba całkowita
- `45000.50` - z kropką dziesiętną
- `45000,50` - z przecinkiem dziesiętnym
- `0.085` - liczba mniejsza od 1

## Przykłady obsługiwanych formatów

### Format podstawowy
```
BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60
```

### Format z emoji
```
🔥 BUY ADAUSDT 🔥
Entry - 0.45
TP - 0.50
SL - 0.40
Timeframe: 120
```

### Format LONG/SHORT
```
LONG SOLUSDT
Entry: 100.25
Take Profit: 105.00
Stop Loss: 95.50
Duration: 240
```

### Format z wieloma TP
```
SHORT BNBUSDT
Entry = 300.00
TP1 = 290.00
TP2 = 285.00
TP3 = 280.00
SL = 310.00
TF: 180
```

### Format z przecinkami
```
BUY DOGEUSDT
Entry: 0,08500
TP: 0,09000
SL: 0,08000
Timeframe: 90
```

### Format z @ symbol
```
💎 SELL XRPUSDT 💎
📈 Entry @ 0.6500
🎯 Target: 0.6200
⚡ Stop: 0.6800
⏰ Time: 45
```

### Format bez timeframe
```
BUY LINKUSDT
Entry - 15.50
TP - 16.00
SL - 15.00
```
(Automatycznie używa domyślnego timeframe: 60 minut)

## Debugging i logowanie

System loguje szczegółowe informacje o parsowaniu:
- Ostrzeżenia walidacji
- Użyty wzorzec regex
- Błędy parsowania
- Informacje o nierozpoznanych sygnałach zawierających słowa kluczowe

Logi są zapisywane do pliku `signal_monitor.log` oraz wyświetlane w konsoli.

## Testowanie

Użyj skryptu `test_signal_parser.py` do testowania różnych formatów sygnałów:

```bash
python test_signal_parser.py
```

Skrypt testuje wszystkie obsługiwane formaty i pokazuje wyniki parsowania wraz z ostrzeżeniami walidacji.
