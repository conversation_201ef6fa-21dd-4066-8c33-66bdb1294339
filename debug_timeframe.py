#!/usr/bin/env python3
"""
Debug timeframe parsing w sygnałach.
"""

import re
import os
from dotenv import load_dotenv

# Ładowanie zmiennych środowiskowych
load_dotenv()
SIGNAL_VALIDITY_HOURS = int(os.getenv('SIGNAL_VALIDITY_HOURS', 48))

# Wzorce regex z głównego pliku
class SignalPatterns:
    """Klasa zawierająca różne wzorce regex do parsowania sygnałów."""

    # Główny pattern - elastyczny format
    MAIN_PATTERN = re.compile(
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*[-:=\s]*(?P<tp>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:Time(?:frame)?|TF|Duration)[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

    # Pattern dla prostych sygnałów (bez timeframe)
    SIMPLE_PATTERN = re.compile(
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*[-:=\s]*(?P<tp>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

SIGNAL_PATTERNS = [
    SignalPatterns.MAIN_PATTERN,
    SignalPatterns.SIMPLE_PATTERN
]

def test_timeframe_parsing():
    """Test parsowania timeframe z różnych przykładów."""

    # Przykładowe sygnały
    test_signals = [
        "BUY BTCUSDT @ 50000 TP 51000 SL 49000 TF 1",
        "SELL ETHUSDT Entry: 3000 TP: 2900 SL: 3100 Timeframe: 5",
        "BUY ADAUSDT @ 1.5 TP 1.6 SL 1.4 Duration 15",
        "LONG SOLUSDT @ 100 TP 105 SL 95",  # Bez timeframe
        "🔥 BUY DOTUSDT @ 10 TP 11 SL 9 TF: 30",
        "SELL LINKUSDT Entry 20 TP 19 SL 21 Time 60"
    ]

    print(f"SIGNAL_VALIDITY_HOURS: {SIGNAL_VALIDITY_HOURS}")
    print(f"Default timeframe (minutes): {SIGNAL_VALIDITY_HOURS * 60}")
    print("\n" + "="*60)

    for i, signal in enumerate(test_signals, 1):
        print(f"\nTest {i}: {signal}")
        print("-" * 40)

        for j, pattern in enumerate(SIGNAL_PATTERNS):
            match = pattern.search(signal)
            if match:
                data = match.groupdict()
                print(f"Pattern {j+1} matched:")
                print(f"  Side: {data.get('side')}")
                print(f"  Pair: {data.get('pair')}")
                print(f"  Entry: {data.get('entry')}")
                print(f"  TP: {data.get('tp')}")
                print(f"  SL: {data.get('sl')}")
                print(f"  TF raw: {data.get('tf')}")

                # Nowa logika timeframe z głównego kodu
                chart_timeframe = None
                if 'tf' in data and data['tf']:
                    chart_timeframe = int(data['tf'])  # TF wykresu (1min, 5min, etc.)

                # Ważność sygnału - zawsze 48 godzin niezależnie od TF wykresu
                signal_validity_minutes = SIGNAL_VALIDITY_HOURS * 60  # 48h = 2880 minut

                print(f"  Chart TF: {chart_timeframe}m" if chart_timeframe else "  Chart TF: None")
                print(f"  Signal validity: {signal_validity_minutes} minutes (48h)")
                break
        else:
            print("No pattern matched")

if __name__ == '__main__':
    test_timeframe_parsing()
