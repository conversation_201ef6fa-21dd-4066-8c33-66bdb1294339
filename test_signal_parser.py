#!/usr/bin/env python3
"""
Test skryptu parsowania sygnałów bez potrzeby połączenia z Discord/Bybit.
Testuje tylko funkcjonalność regex i parsowania sygnałów.
"""

import re
import sys
import os

# Dodaj ścieżkę do głównego skryptu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_parsing():
    """Test parsowania różnych formatów sygnałów z nowym zaawansowanym parserem."""

    # Import funkcji z głównego skryptu
    try:
        from discord_bybit_signal_monitor import parse_signal_advanced
    except ImportError:
        print("❌ Nie można zaimportować funkcji parse_signal_advanced")
        return

    # Rozszerzone przykładowe sygnały do testowania
    test_signals = [
        # Podstawowy format
        """BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60""",

        # Format z .P
        """SELL ETH.P
Entry - 2500.50
TP - 2400.00
SL - 2600.00
Time: 30""",

        # Format z dodatkowym tekstem
        """🔥 BUY ADAUSDT 🔥
Some additional text here
Entry - 0.45
TP - 0.50
SL - 0.40
Timeframe: 120""",

        # Format z LONG/SHORT
        """LONG SOLUSDT
Entry: 100.25
Take Profit: 105.00
Stop Loss: 95.50
Duration: 240""",

        # Format z wieloma TP
        """SHORT BNBUSDT
Entry = 300.00
TP1 = 290.00
TP2 = 285.00
TP3 = 280.00
SL = 310.00
TF: 180""",

        # Test dodatkowy dla wielokrotnych TP
        """BUY ETHUSDT
Entry: 2000
TP1: 2100
TP2: 2200
SL: 1900
Timeframe: 120""",

        # Format z przecinkami jako separatory dziesiętne
        """BUY DOGEUSDT
Entry: 0,08500
TP: 0,09000
SL: 0,08000
Timeframe: 90""",

        # Format z emoji i formatowaniem
        """💎 SELL XRPUSDT 💎
📈 Entry @ 0.6500
🎯 Target: 0.6200
⚡ Stop: 0.6800
⏰ Time: 45""",

        # Format bez timeframe (powinien użyć domyślnego)
        """BUY LINKUSDT
Entry - 15.50
TP - 16.00
SL - 15.00""",

        # Niepoprawny sygnał
        """This is not a signal
Just some random text""",

        # Sygnał z błędną logiką (TP niższe niż entry dla BUY)
        """BUY BTCUSDT
Entry - 45000
TP - 44000
SL - 43000
Timeframe: 60"""
    ]

    print("🧪 Test zaawansowanego parsowania sygnałów tradingowych\n")

    for i, signal_text in enumerate(test_signals, 1):
        print(f"Test {i}:")
        print(f"Tekst: {repr(signal_text[:60])}...")

        signal_data = parse_signal_advanced(signal_text)

        if signal_data:
            print(f"✅ Rozpoznano sygnał:")
            print(f"   Para: {signal_data['pair']}")
            print(f"   Kierunek: {signal_data['side']}")
            print(f"   Entry: {signal_data['entry']}")
            print(f"   TP: {signal_data['tp']}")
            if 'tp2' in signal_data:
                print(f"   TP2: {signal_data['tp2']}")
            if 'tp3' in signal_data:
                print(f"   TP3: {signal_data['tp3']}")
            print(f"   SL: {signal_data['sl']}")
            print(f"   Timeframe: {signal_data['timeframe_min']} min")

            # Wyświetl ostrzeżenia
            if signal_data.get('warnings'):
                print(f"⚠️  Ostrzeżenia ({len(signal_data['warnings'])}):")
                for warning in signal_data['warnings']:
                    print(f"     - {warning}")
            else:
                print("✅ Brak ostrzeżeń walidacji")

            # Informacja o użytym wzorcu (dla debugowania)
            if 'pattern_used' in signal_data:
                print(f"🔍 Użyty wzorzec: {signal_data['pattern_used']}")

        else:
            print("❌ Nie rozpoznano sygnału")

        print("-" * 70)

def test_database_connection():
    """Test połączenia z bazą danych."""
    import sqlite3

    print("\n🗄️  Test połączenia z bazą danych")

    try:
        conn = sqlite3.connect('signals.db')
        cursor = conn.cursor()

        # Sprawdź strukturę tabeli
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='signals'")
        table_exists = cursor.fetchone()

        if table_exists:
            print("✅ Tabela 'signals' istnieje")

            # Sprawdź liczbę rekordów
            cursor.execute("SELECT COUNT(*) FROM signals")
            count = cursor.fetchone()[0]
            print(f"📊 Liczba sygnałów w bazie: {count}")

            # Pokaż ostatnie 3 sygnały
            if count > 0:
                cursor.execute("SELECT pair, side, entry, status FROM signals ORDER BY timestamp DESC LIMIT 3")
                recent = cursor.fetchall()
                print("🔍 Ostatnie sygnały:")
                for signal in recent:
                    print(f"   {signal[1]} {signal[0]} @ {signal[2]} (status: {signal[3]})")
        else:
            print("❌ Tabela 'signals' nie istnieje")

        conn.close()
        print("✅ Połączenie z bazą danych działa poprawnie")

    except Exception as e:
        print(f"❌ Błąd połączenia z bazą danych: {e}")

if __name__ == '__main__':
    test_signal_parsing()
    test_database_connection()
    print("\n🎉 Testy zakończone!")
