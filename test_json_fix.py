#!/usr/bin/env python3
"""
Test skryptu naprawy JSON dla wartości NaN i Infinity.

Data: 2025-06-14
"""

import math
import json
import numpy as np
import pandas as pd

def sanitize_for_json(obj):
    """
    Sanityzuje obiekt do bezpiecznego formatu JSON, zastępując NaN i Infinity.

    Args:
        obj: Obiekt do sanityzacji (może być dict, list, float, int, etc.)

    Returns:
        Obiekt z zastąpionymi problematycznymi wartościami
    """
    if isinstance(obj, dict):
        return {key: sanitize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, float):
        if math.isnan(obj):
            return 0.0  # Zastąp NaN zerem
        elif math.isinf(obj):
            if obj > 0:
                return 999999.0  # Zastąp +Infinity dużą liczbą
            else:
                return -999999.0  # Zastąp -Infinity dużą ujemną liczbą
        else:
            return obj
    elif isinstance(obj, np.floating):
        if np.isnan(obj):
            return 0.0
        elif np.isinf(obj):
            if obj > 0:
                return 999999.0
            else:
                return -999999.0
        else:
            return float(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    else:
        return obj

def test_sanitization():
    """Test funkcji sanityzacji."""
    print("🧪 Testowanie funkcji sanityzacji JSON...")

    # Test przypadków problematycznych
    test_cases = [
        # Podstawowe przypadki
        {"name": "Normal float", "input": 3.14, "expected": 3.14},
        {"name": "Normal int", "input": 42, "expected": 42},
        {"name": "Normal string", "input": "test", "expected": "test"},

        # Problematyczne przypadki
        {"name": "NaN", "input": float('nan'), "expected": 0.0},
        {"name": "Positive Infinity", "input": float('inf'), "expected": 999999.0},
        {"name": "Negative Infinity", "input": float('-inf'), "expected": -999999.0},

        # NumPy przypadki
        {"name": "NumPy NaN", "input": np.nan, "expected": 0.0},
        {"name": "NumPy Inf", "input": np.inf, "expected": 999999.0},
        {"name": "NumPy -Inf", "input": np.NINF, "expected": -999999.0},
        {"name": "NumPy float64", "input": np.float64(3.14), "expected": 3.14},
        {"name": "NumPy int64", "input": np.int64(42), "expected": 42},

        # Złożone struktury
        {
            "name": "Dict with NaN",
            "input": {"avg_pnl": float('nan'), "profit_factor": float('inf')},
            "expected": {"avg_pnl": 0.0, "profit_factor": 999999.0}
        },
        {
            "name": "List with problematic values",
            "input": [1.0, float('nan'), float('inf'), 3.14],
            "expected": [1.0, 0.0, 999999.0, 3.14]
        },
        {
            "name": "Nested structure",
            "input": {
                "stats": {
                    "sharpe_ratio": float('nan'),
                    "profit_factor": float('inf'),
                    "values": [1.0, float('nan'), 2.0]
                }
            },
            "expected": {
                "stats": {
                    "sharpe_ratio": 0.0,
                    "profit_factor": 999999.0,
                    "values": [1.0, 0.0, 2.0]
                }
            }
        }
    ]

    passed = 0
    failed = 0

    for test_case in test_cases:
        try:
            result = sanitize_for_json(test_case["input"])

            # Sprawdź czy wynik można serializować do JSON
            json_str = json.dumps(result)

            # Sprawdź czy wynik jest zgodny z oczekiwaniem
            if result == test_case["expected"]:
                print(f"✅ {test_case['name']}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_case['name']}: FAILED")
                print(f"   Expected: {test_case['expected']}")
                print(f"   Got: {result}")
                failed += 1

        except Exception as e:
            print(f"❌ {test_case['name']}: ERROR - {e}")
            failed += 1

    print(f"\n📊 Wyniki testów: {passed} passed, {failed} failed")
    return failed == 0

def test_real_world_scenario():
    """Test scenariusza z prawdziwymi danymi."""
    print("\n🌍 Testowanie scenariusza z prawdziwymi danymi...")

    # Symuluj dane które mogą powodować problemy
    problematic_data = {
        "sharpe_ratio": float('nan'),
        "max_drawdown": 0.15,
        "profit_factor": float('inf'),
        "avg_win": float('nan'),
        "avg_loss": -0.02,
        "pair_stats": [
            {"pair": "BTCUSDT", "avg_pnl": float('nan'), "total_pnl": 0.05},
            {"pair": "ETHUSDT", "avg_pnl": 0.03, "total_pnl": float('inf')}
        ]
    }

    print("Dane przed sanityzacją:")
    print(f"  sharpe_ratio: {problematic_data['sharpe_ratio']}")
    print(f"  profit_factor: {problematic_data['profit_factor']}")
    print(f"  avg_win: {problematic_data['avg_win']}")

    try:
        # Próba serializacji bez sanityzacji z allow_nan=False (powinna się nie udać)
        json.dumps(problematic_data, allow_nan=False)
        print("❌ Serializacja bez sanityzacji się udała (nie powinna!)")
        return False
    except (ValueError, TypeError) as e:
        print(f"✅ Serializacja bez sanityzacji nie udała się (oczekiwane): {e}")

    # Sanityzacja i ponowna próba
    sanitized_data = sanitize_for_json(problematic_data)

    try:
        json_str = json.dumps(sanitized_data, indent=2)
        print("✅ Serializacja po sanityzacji się udała!")
        print("Dane po sanityzacji:")
        print(f"  sharpe_ratio: {sanitized_data['sharpe_ratio']}")
        print(f"  profit_factor: {sanitized_data['profit_factor']}")
        print(f"  avg_win: {sanitized_data['avg_win']}")
        return True
    except Exception as e:
        print(f"❌ Serializacja po sanityzacji nie udała się: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Test naprawy JSON dla wartości NaN i Infinity")
    print("=" * 50)

    success1 = test_sanitization()
    success2 = test_real_world_scenario()

    if success1 and success2:
        print("\n🎉 Wszystkie testy przeszły pomyślnie!")
        print("✅ Funkcja sanityzacji działa poprawnie")
        print("✅ Dashboard powinien teraz działać bez błędów JSON")
    else:
        print("\n❌ Niektóre testy nie przeszły")
        print("🔧 Wymagane są dodatkowe poprawki")
