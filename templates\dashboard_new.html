<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Discord Bybit Signal Monitor - Professional Dashboard</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
      /* === RESET & BASE STYLES === */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        /* Dark Trading Theme Colors */
        --bg-primary: #0a0e1a;
        --bg-secondary: #1a1f2e;
        --bg-tertiary: #252b3d;
        --bg-card: #1e2332;
        --bg-hover: #2a3142;

        /* Accent Colors */
        --accent-primary: #00d4aa;
        --accent-secondary: #0099ff;
        --accent-danger: #ff4757;
        --accent-warning: #ffa502;
        --accent-success: #2ed573;

        /* Text Colors */
        --text-primary: #ffffff;
        --text-secondary: #a0a9c0;
        --text-muted: #6c7293;

        /* Border Colors */
        --border-primary: #2a3142;
        --border-secondary: #3a4158;

        /* Gradients */
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

        /* Shadows */
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
        --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.25);
        --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.35);

        /* Transitions */
        --transition-fast: 0.15s ease;
        --transition-normal: 0.3s ease;
        --transition-slow: 0.5s ease;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        line-height: 1.6;
        overflow-x: hidden;
      }

      /* === SCROLLBAR STYLING === */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: var(--bg-secondary);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--accent-primary);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--accent-secondary);
      }

      /* === UTILITY CLASSES === */
      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .grid {
        display: grid;
        gap: 20px;
      }

      .grid-cols-1 {
        grid-template-columns: repeat(1, 1fr);
      }
      .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }
      .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }
      .grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
      }
      .grid-cols-6 {
        grid-template-columns: repeat(6, 1fr);
      }

      .flex {
        display: flex;
      }

      .flex-col {
        flex-direction: column;
      }

      .items-center {
        align-items: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .justify-center {
        justify-content: center;
      }

      .text-center {
        text-align: center;
      }

      .text-right {
        text-align: right;
      }

      .font-bold {
        font-weight: 600;
      }

      .font-semibold {
        font-weight: 500;
      }

      .text-sm {
        font-size: 0.875rem;
      }

      .text-lg {
        font-size: 1.125rem;
      }

      .text-xl {
        font-size: 1.25rem;
      }

      .text-2xl {
        font-size: 1.5rem;
      }

      .text-3xl {
        font-size: 1.875rem;
      }

      .mb-2 {
        margin-bottom: 0.5rem;
      }

      .mb-4 {
        margin-bottom: 1rem;
      }

      .mb-6 {
        margin-bottom: 1.5rem;
      }

      .mt-4 {
        margin-top: 1rem;
      }

      .p-4 {
        padding: 1rem;
      }

      .p-6 {
        padding: 1.5rem;
      }

      .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
      }

      .py-3 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
      }

      .rounded {
        border-radius: 8px;
      }

      .rounded-lg {
        border-radius: 12px;
      }

      .rounded-xl {
        border-radius: 16px;
      }

      .shadow-lg {
        box-shadow: var(--shadow-lg);
      }

      .transition {
        transition: var(--transition-normal);
      }

      /* === COMPONENT STYLES === */
      .navbar {
        background: var(--bg-card);
        border-bottom: 1px solid var(--border-primary);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
        backdrop-filter: blur(10px);
      }

      .navbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .navbar-brand {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      .live-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--accent-success);
        animation: pulse 2s infinite;
        box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7);
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(46, 213, 115, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(46, 213, 115, 0);
        }
      }

      .btn {
        padding: 8px 16px;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-normal);
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .btn-primary {
        background: var(--gradient-primary);
        color: white;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-outline {
        background: transparent;
        border: 1px solid var(--border-secondary);
        color: var(--text-secondary);
      }

      .btn-outline:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }

      .btn-outline.active {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
      }

      .card {
        background: var(--bg-card);
        border-radius: 16px;
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        transition: var(--transition-normal);
        overflow: hidden;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .card-header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid var(--border-primary);
        background: var(--bg-tertiary);
      }

      .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .card-body {
        padding: 24px;
      }

      /* === STAT CARDS === */
      .stat-card {
        background: var(--bg-card);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid var(--border-primary);
        position: relative;
        overflow: hidden;
        transition: var(--transition-normal);
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
      }

      .stat-card.success::before {
        background: var(--gradient-success);
      }

      .stat-card.danger::before {
        background: var(--gradient-danger);
      }

      .stat-card.warning::before {
        background: var(--gradient-warning);
      }

      .stat-card.info::before {
        background: var(--gradient-info);
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 16px;
        background: var(--bg-tertiary);
        color: var(--accent-primary);
      }

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .stat-change {
        font-size: 0.75rem;
        font-weight: 600;
        margin-top: 8px;
      }

      .stat-change.positive {
        color: var(--accent-success);
      }

      .stat-change.negative {
        color: var(--accent-danger);
      }

      /* === RESPONSIVE DESIGN === */
      @media (max-width: 768px) {
        .container {
          padding: 0 16px;
        }

        .grid-cols-4 {
          grid-template-columns: repeat(2, 1fr);
        }

        .grid-cols-6 {
          grid-template-columns: repeat(3, 1fr);
        }

        .navbar-brand {
          font-size: 1rem;
        }

        .stat-card {
          padding: 16px;
        }

        .stat-value {
          font-size: 1.5rem;
        }
      }

      @media (max-width: 480px) {
        .grid-cols-2,
        .grid-cols-3,
        .grid-cols-4,
        .grid-cols-6 {
          grid-template-columns: 1fr;
        }
      }

      /* === FORM ELEMENTS === */
      .form-select {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-secondary);
        border-radius: 8px;
        padding: 8px 12px;
        color: var(--text-primary);
        font-size: 0.875rem;
        transition: var(--transition-normal);
        width: 100%;
      }

      .form-select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
      }

      .form-select option {
        background: var(--bg-tertiary);
        color: var(--text-primary);
      }

      input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: var(--accent-primary);
      }

      /* === TABLE STYLES === */
      .signals-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
      }

      .signals-table th {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-weight: 600;
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-primary);
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .signals-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-primary);
        color: var(--text-secondary);
        transition: var(--transition-fast);
      }

      .signals-table tbody tr:hover {
        background: var(--bg-hover);
      }

      .signals-table tbody tr:hover td {
        color: var(--text-primary);
      }

      /* === STATUS BADGES === */
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .status-new {
        background: rgba(108, 114, 147, 0.2);
        color: var(--text-muted);
        border: 1px solid var(--text-muted);
      }

      .status-entry-hit {
        background: rgba(0, 153, 255, 0.2);
        color: var(--accent-secondary);
        border: 1px solid var(--accent-secondary);
      }

      .status-tp-hit {
        background: rgba(46, 213, 115, 0.2);
        color: var(--accent-success);
        border: 1px solid var(--accent-success);
      }

      .status-sl-hit {
        background: rgba(255, 71, 87, 0.2);
        color: var(--accent-danger);
        border: 1px solid var(--accent-danger);
      }

      .status-expired {
        background: rgba(255, 165, 2, 0.2);
        color: var(--accent-warning);
        border: 1px solid var(--accent-warning);
      }

      /* === PNL STYLING === */
      .pnl-positive {
        color: var(--accent-success);
        font-weight: 600;
      }

      .pnl-negative {
        color: var(--accent-danger);
        font-weight: 600;
      }

      .pnl-neutral {
        color: var(--text-muted);
      }

      /* === SIGNAL STATUS INDICATORS === */
      .signal-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .signal-status.new {
        background: var(--text-muted);
      }

      .signal-status.entry-hit {
        background: var(--accent-secondary);
      }

      .signal-status.tp-hit {
        background: var(--accent-success);
      }

      .signal-status.sl-hit {
        background: var(--accent-danger);
      }

      .signal-status.expired {
        background: var(--accent-warning);
      }

      /* === RECENT SIGNALS & ACTIVITY === */
      .recent-signal {
        padding: 12px;
        background: var(--bg-tertiary);
        border-radius: 8px;
        border: 1px solid var(--border-primary);
        transition: var(--transition-normal);
      }

      .recent-signal:hover {
        background: var(--bg-hover);
        transform: translateX(4px);
      }

      .activity-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-primary);
      }

      .activity-item:last-child {
        border-bottom: none;
      }

      /* === ACTION BUTTONS === */
      .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-size: 0.75rem;
        cursor: pointer;
        transition: var(--transition-fast);
        margin: 0 2px;
      }

      .action-btn.view {
        background: var(--accent-secondary);
        color: white;
      }

      .action-btn.edit {
        background: var(--accent-warning);
        color: white;
      }

      .action-btn.delete {
        background: var(--accent-danger);
        color: white;
      }

      .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
      }

      /* === LOADING STATES === */
      .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .fa-spin {
        animation: spin 1s linear infinite;
      }

      /* === CHART CUSTOMIZATIONS === */
      .chart-container {
        position: relative;
        background: var(--bg-tertiary);
        border-radius: 8px;
        padding: 16px;
      }

      /* === BADGE STYLES === */
      .badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
        background: var(--accent-primary);
        color: white;
      }

      /* === ADDITIONAL RESPONSIVE ADJUSTMENTS === */
      @media (max-width: 1024px) {
        .grid-cols-3 {
          grid-template-columns: 1fr;
        }

        .grid-cols-4 {
          grid-template-columns: repeat(2, 1fr);
        }

        .grid-cols-6 {
          grid-template-columns: repeat(3, 1fr);
        }
      }

      @media (max-width: 768px) {
        .signals-table {
          font-size: 0.75rem;
        }

        .signals-table th,
        .signals-table td {
          padding: 8px 12px;
        }

        .stat-card {
          padding: 16px;
        }

        .card-body {
          padding: 16px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <div class="navbar-content">
          <div class="navbar-brand">
            <i class="fas fa-chart-line"></i>
            <span>Discord Bybit Signal Monitor</span>
            <div class="live-indicator" id="liveIndicator"></div>
            <span class="text-sm" style="color: var(--text-muted)">Live</span>
          </div>
          <button class="btn btn-primary" onclick="refreshData()">
            <i class="fas fa-sync-alt"></i>
            <span>Odśwież</span>
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
      <!-- Time Filter Section -->
      <div class="card mb-6">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold">Okres analizy</h3>
            <div class="flex gap-2" id="timeFilterButtons">
              <button
                class="btn btn-outline active"
                onclick="setTimeFilter(null)"
              >
                Wszystko
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(1)">
                24h
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(7)">
                7 dni
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(30)">
                30 dni
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Statistics Grid -->
      <div class="grid grid-cols-4 mb-6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-signal"></i>
          </div>
          <div class="stat-value" id="totalSignals">-</div>
          <div class="stat-label">Wszystkie sygnały</div>
          <div class="stat-change positive" id="totalSignalsChange">
            <i class="fas fa-arrow-up"></i> +12% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="stat-value" id="winRate">-</div>
          <div class="stat-label">Win Rate</div>
          <div class="stat-change positive" id="winRateChange">
            <i class="fas fa-arrow-up"></i> ****% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="stat-value" id="avgPnl">-</div>
          <div class="stat-label">Średni PnL</div>
          <div class="stat-change positive" id="avgPnlChange">
            <i class="fas fa-arrow-up"></i> ****% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="stat-value" id="totalPnl">-</div>
          <div class="stat-label">Całkowity PnL</div>
          <div class="stat-change positive" id="totalPnlChange">
            <i class="fas fa-arrow-up"></i> ****% vs poprzedni okres
          </div>
        </div>
      </div>

      <!-- Advanced Metrics Grid -->
      <div class="grid grid-cols-6 mb-6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-value text-lg" id="sharpeRatio">-</div>
          <div class="stat-label">Sharpe Ratio</div>
        </div>

        <div class="stat-card danger">
          <div class="stat-icon">
            <i class="fas fa-arrow-down"></i>
          </div>
          <div class="stat-value text-lg" id="maxDrawdown">-</div>
          <div class="stat-label">Max Drawdown</div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="stat-value text-lg" id="maxWins">-</div>
          <div class="stat-label">Max Wins</div>
        </div>

        <div class="stat-card danger">
          <div class="stat-icon">
            <i class="fas fa-snowflake"></i>
          </div>
          <div class="stat-value text-lg" id="maxLosses">-</div>
          <div class="stat-label">Max Losses</div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <div class="stat-value text-lg" id="profitFactor">-</div>
          <div class="stat-label">Profit Factor</div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-value text-lg" id="openSignals">-</div>
          <div class="stat-label">Otwarte</div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-3 mb-6">
        <div class="card" style="grid-column: span 2">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-line"></i>
              Equity Curve (Krzywa kapitału)
            </h3>
          </div>
          <div class="card-body">
            <div style="position: relative; height: 400px">
              <canvas id="pnlChart"></canvas>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-pie-chart"></i>
              Rozkład wyników
            </h3>
          </div>
          <div class="card-body">
            <div style="position: relative; height: 400px">
              <canvas id="statusChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Charts -->
      <div class="grid grid-cols-2 mb-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-bar"></i>
              Rozkład PnL
            </h3>
          </div>
          <div class="card-body">
            <div style="position: relative; height: 300px">
              <canvas id="pnlDistributionChart"></canvas>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-coins"></i>
              Wydajność per para
            </h3>
          </div>
          <div class="card-body">
            <div style="position: relative; height: 300px">
              <canvas id="pairPerformanceChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Configuration Panel -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-cog"></i>
            Panel konfiguracji
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-3">
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Whitelist botów Discord:</label
              >
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                  <input type="checkbox" id="bot1" checked />
                  <label for="bot1" class="text-sm">TradingBot#1234</label>
                  <span class="text-sm" style="color: var(--accent-success)"
                    >●</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" id="bot2" checked />
                  <label for="bot2" class="text-sm">SignalMaster#5678</label>
                  <span class="text-sm" style="color: var(--accent-success)"
                    >●</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" id="bot3" />
                  <label for="bot3" class="text-sm">CryptoSignals#9012</label>
                  <span class="text-sm" style="color: var(--text-muted)"
                    >●</span
                  >
                </div>
              </div>
            </div>
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Parametry timeoutów:</label
              >
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                  <span class="text-sm">Timeout sygnałów:</span>
                  <span class="font-bold" style="color: var(--accent-warning)"
                    >48h</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">Sprawdzanie cen:</span>
                  <span class="font-bold" style="color: var(--accent-info)"
                    >30s</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">Aktywne kanały:</span>
                  <span class="font-bold" style="color: var(--accent-success)"
                    >3</span
                  >
                </div>
              </div>
            </div>
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Status systemu:</label
              >
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                  <span class="text-sm">Discord Bot:</span>
                  <span class="font-bold" style="color: var(--accent-success)"
                    >Online</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">ByBit API:</span>
                  <span class="font-bold" style="color: var(--accent-success)"
                    >Connected</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">Database:</span>
                  <span class="font-bold" style="color: var(--accent-success)"
                    >Active</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-filter"></i>
            Filtry i eksport danych
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-4">
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Status:</label
              >
              <select
                class="form-select"
                id="statusFilter"
                onchange="filterSignals()"
              >
                <option value="all">Wszystkie</option>
                <option value="NEW">Nowe</option>
                <option value="ENTRY_HIT">Entry Hit</option>
                <option value="TP_HIT">Take Profit</option>
                <option value="SL_HIT">Stop Loss</option>
                <option value="EXPIRED">Wygasłe</option>
              </select>
            </div>
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Para:</label
              >
              <select
                class="form-select"
                id="pairFilter"
                onchange="filterSignals()"
              >
                <option value="all">Wszystkie</option>
              </select>
            </div>
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Limit:</label
              >
              <select
                class="form-select"
                id="limitFilter"
                onchange="filterSignals()"
              >
                <option value="">Wszystkie</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="500">500</option>
              </select>
            </div>
            <div>
              <label class="stat-label mb-2" style="display: block"
                >Akcje:</label
              >
              <button
                class="btn btn-primary"
                onclick="exportData()"
                style="width: 100%"
              >
                <i class="fas fa-download"></i>
                Eksport CSV
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Signals Table -->
      <div class="card mb-6">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title">
              <i class="fas fa-table"></i>
              Historia sygnałów
            </h3>
            <span
              class="badge"
              id="signalCount"
              style="
                background: var(--accent-primary);
                color: white;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 0.875rem;
              "
              >0</span
            >
          </div>
        </div>
        <div class="card-body">
          <div style="overflow-x: auto">
            <table class="signals-table" id="signalsTable">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Para</th>
                  <th>Kierunek</th>
                  <th>Entry</th>
                  <th>TP</th>
                  <th>SL</th>
                  <th>Status</th>
                  <th>PnL</th>
                  <th>Data</th>
                  <th>Akcje</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td colspan="10" class="text-center" style="padding: 40px">
                    <div class="loading-spinner">
                      <i
                        class="fas fa-spinner fa-spin fa-2x"
                        style="color: var(--accent-primary)"
                      ></i>
                      <div
                        style="margin-top: 16px; color: var(--text-secondary)"
                      >
                        Ładowanie sygnałów...
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Live Monitoring Panel -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-satellite-dish"></i>
            Panel monitorowania na żywo
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-2">
            <div>
              <h4 class="font-semibold mb-4">Ostatnie sygnały</h4>
              <div id="recentSignals" class="flex flex-col gap-3">
                <div class="recent-signal">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="signal-status new"></div>
                      <div>
                        <div class="font-semibold">BTCUSDT</div>
                        <div
                          class="text-sm"
                          style="color: var(--text-secondary)"
                        >
                          BUY @ 43,250
                        </div>
                      </div>
                    </div>
                    <div class="text-sm" style="color: var(--text-muted)">
                      2 min temu
                    </div>
                  </div>
                </div>
                <div class="recent-signal">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="signal-status tp-hit"></div>
                      <div>
                        <div class="font-semibold">ETHUSDT</div>
                        <div
                          class="text-sm"
                          style="color: var(--text-secondary)"
                        >
                          SELL @ 2,650
                        </div>
                      </div>
                    </div>
                    <div class="text-sm" style="color: var(--accent-success)">
                      +2.4%
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 class="font-semibold mb-4">Aktywność systemu</h4>
              <div class="activity-log" id="activityLog">
                <div class="activity-item">
                  <div class="flex items-center gap-3">
                    <i
                      class="fas fa-plus-circle"
                      style="color: var(--accent-success)"
                    ></i>
                    <div>
                      <div class="text-sm">Nowy sygnał zarejestrowany</div>
                      <div class="text-sm" style="color: var(--text-muted)">
                        BTCUSDT BUY
                      </div>
                    </div>
                  </div>
                  <div class="text-sm" style="color: var(--text-muted)">
                    14:32
                  </div>
                </div>
                <div class="activity-item">
                  <div class="flex items-center gap-3">
                    <i
                      class="fas fa-check-circle"
                      style="color: var(--accent-success)"
                    ></i>
                    <div>
                      <div class="text-sm">Take Profit osiągnięty</div>
                      <div class="text-sm" style="color: var(--text-muted)">
                        ETHUSDT +2.4%
                      </div>
                    </div>
                  </div>
                  <div class="text-sm" style="color: var(--text-muted)">
                    14:28
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script>
      // === GLOBAL VARIABLES ===
      const socket = io();
      let pnlChart, statusChart, pnlDistributionChart, pairPerformanceChart;
      let currentSignals = [];
      let currentTimeFilter = null;

      // === INITIALIZATION ===
      document.addEventListener("DOMContentLoaded", function () {
        try {
          console.log("🚀 Initializing Professional Dashboard...");
          initCharts();
          loadData();
          loadPairs();
          setupEventListeners();
          console.log("✅ Dashboard initialized successfully");
        } catch (error) {
          console.error("❌ Error initializing dashboard:", error);
          showNotification("Błąd inicjalizacji dashboardu", "error");
        }
      });

      // === SOCKET.IO EVENT HANDLERS ===
      socket.on("connect", function () {
        console.log("🔗 Connected to server");
        updateConnectionStatus(true);
        showNotification("Połączono z serwerem", "success");
      });

      socket.on("disconnect", function () {
        console.log("❌ Disconnected from server");
        updateConnectionStatus(false);
        showNotification("Rozłączono z serwerem", "warning");
      });

      socket.on("new_signal", function (data) {
        console.log("📈 New signal received:", data);
        loadData();
        addRecentSignal(data);
        addActivityLog(
          "Nowy sygnał zarejestrowany",
          `${data.pair} ${data.side}`
        );
        showNotification(`Nowy sygnał: ${data.pair} ${data.side}`, "info");
      });

      socket.on("signal_update", function (data) {
        console.log("🔄 Signal updated:", data);
        loadData();
        addActivityLog("Sygnał zaktualizowany", `${data.pair} ${data.status}`);
        if (data.status === "TP_HIT") {
          showNotification(
            `Take Profit: ${data.pair} +${(data.pnl * 100).toFixed(2)}%`,
            "success"
          );
        } else if (data.status === "SL_HIT") {
          showNotification(
            `Stop Loss: ${data.pair} ${(data.pnl * 100).toFixed(2)}%`,
            "error"
          );
        }
      });

      // === MAIN FUNCTIONS ===
      async function loadData() {
        try {
          await Promise.all([
            loadStatistics(),
            loadSignals(),
            loadPnlChart(),
            loadPnlDistribution(),
            loadPairPerformance(),
            loadPerformanceMetrics(),
          ]);
        } catch (error) {
          console.error("Error loading data:", error);
          showNotification("Błąd ładowania danych", "error");
        }
      }

      function setTimeFilter(days) {
        currentTimeFilter = days;
        updateActiveTimeFilter(days);
        loadData();
      }

      function updateActiveTimeFilter(days) {
        document.querySelectorAll("#timeFilterButtons .btn").forEach((btn) => {
          btn.classList.remove("active");
        });

        if (days === null) {
          document
            .querySelector("#timeFilterButtons .btn:first-child")
            .classList.add("active");
        } else {
          document
            .querySelector(
              `#timeFilterButtons .btn[onclick="setTimeFilter(${days})"]`
            )
            .classList.add("active");
        }
      }

      async function loadStatistics() {
        try {
          const url = currentTimeFilter
            ? `/api/statistics?days=${currentTimeFilter}`
            : "/api/statistics";

          const response = await fetch(url);
          const stats = await response.json();

          // Update main statistics
          updateElement("totalSignals", stats.total_signals || 0);
          updateElement("winRate", `${(stats.win_rate || 0).toFixed(1)}%`);
          updateElement(
            "avgPnl",
            `${((stats.avg_pnl || 0) * 100).toFixed(2)}%`
          );
          updateElement(
            "totalPnl",
            `${((stats.total_pnl || 0) * 100).toFixed(2)}%`
          );

          // Update advanced metrics
          updateElement("sharpeRatio", (stats.sharpe_ratio || 0).toFixed(2));
          updateElement(
            "maxDrawdown",
            `${((stats.max_drawdown || 0) * 100).toFixed(1)}%`
          );
          updateElement("maxWins", stats.max_consecutive_wins || 0);
          updateElement("maxLosses", stats.max_consecutive_losses || 0);
          updateElement("profitFactor", (stats.profit_factor || 0).toFixed(2));
          updateElement(
            "openSignals",
            (stats.total_signals || 0) - (stats.closed_signals || 0)
          );

          // Update stat changes (mock data for now)
          updateStatChanges(stats);
        } catch (error) {
          console.error("Error loading statistics:", error);
        }
      }

      async function loadSignals() {
        try {
          const url = currentTimeFilter
            ? `/api/signals?days=${currentTimeFilter}`
            : "/api/signals";

          const response = await fetch(url);
          const signals = await response.json();

          currentSignals = signals;
          updateSignalsTable(signals);
          updateElement("signalCount", signals.length);
        } catch (error) {
          console.error("Error loading signals:", error);
        }
      }

      function updateSignalsTable(signals) {
        const tbody = document.querySelector("#signalsTable tbody");

        if (signals.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="10" class="text-center" style="padding: 40px;">
                <div style="color: var(--text-muted);">
                  <i class="fas fa-inbox fa-2x" style="margin-bottom: 16px;"></i>
                  <div>Brak sygnałów do wyświetlenia</div>
                </div>
              </td>
            </tr>
          `;
          return;
        }

        tbody.innerHTML = signals
          .map((signal) => {
            const statusClass = getStatusClass(signal.status);
            const pnlClass = getPnlClass(signal.pnl);
            const pnlValue = signal.pnl
              ? `${(signal.pnl * 100).toFixed(2)}%`
              : "-";

            return `
            <tr>
              <td><code>${signal.id}</code></td>
              <td><strong>${signal.pair}</strong></td>
              <td>
                <span class="badge" style="background: ${
                  signal.side === "BUY"
                    ? "var(--accent-success)"
                    : "var(--accent-danger)"
                };">
                  ${signal.side}
                </span>
              </td>
              <td>${signal.entry}</td>
              <td>${signal.tp}</td>
              <td>${signal.sl}</td>
              <td><span class="status-badge ${statusClass}">${
              signal.status
            }</span></td>
              <td class="${pnlClass}">${pnlValue}</td>
              <td>${formatDate(signal.timestamp)}</td>
              <td>
                <button class="action-btn view" onclick="viewSignal('${
                  signal.id
                }')">
                  <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit" onclick="editSignal('${
                  signal.id
                }')">
                  <i class="fas fa-edit"></i>
                </button>
              </td>
            </tr>
          `;
          })
          .join("");
      }

      // === UTILITY FUNCTIONS ===
      function updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
          element.textContent = value;
        }
      }

      function getStatusClass(status) {
        const statusMap = {
          NEW: "status-new",
          ENTRY_HIT: "status-entry-hit",
          TP_HIT: "status-tp-hit",
          SL_HIT: "status-sl-hit",
          EXPIRED: "status-expired",
        };
        return statusMap[status] || "status-new";
      }

      function getPnlClass(pnl) {
        if (!pnl) return "pnl-neutral";
        return pnl > 0 ? "pnl-positive" : "pnl-negative";
      }

      function formatDate(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString("pl-PL", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      function updateConnectionStatus(connected) {
        const indicator = document.getElementById("liveIndicator");
        if (indicator) {
          indicator.style.background = connected
            ? "var(--accent-success)"
            : "var(--accent-danger)";
        }
      }

      function updateStatChanges(stats) {
        // Mock implementation - in real app, compare with previous period
        const changes = [
          { id: "totalSignalsChange", value: "+12%", positive: true },
          { id: "winRateChange", value: "****%", positive: true },
          { id: "avgPnlChange", value: "****%", positive: true },
          { id: "totalPnlChange", value: "****%", positive: true },
        ];

        changes.forEach((change) => {
          const element = document.getElementById(change.id);
          if (element) {
            element.className = `stat-change ${
              change.positive ? "positive" : "negative"
            }`;
            element.innerHTML = `
              <i class="fas fa-arrow-${change.positive ? "up" : "down"}"></i>
              ${change.value} vs poprzedni okres
            `;
          }
        });
      }

      function addRecentSignal(signal) {
        const container = document.getElementById("recentSignals");
        if (!container) return;

        const signalElement = document.createElement("div");
        signalElement.className = "recent-signal";
        signalElement.innerHTML = `
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="signal-status new"></div>
              <div>
                <div class="font-semibold">${signal.pair}</div>
                <div class="text-sm" style="color: var(--text-secondary);">
                  ${signal.side} @ ${signal.entry}
                </div>
              </div>
            </div>
            <div class="text-sm" style="color: var(--text-muted);">Teraz</div>
          </div>
        `;

        container.insertBefore(signalElement, container.firstChild);

        // Keep only last 5 signals
        while (container.children.length > 5) {
          container.removeChild(container.lastChild);
        }
      }

      function addActivityLog(action, details) {
        const container = document.getElementById("activityLog");
        if (!container) return;

        const activityElement = document.createElement("div");
        activityElement.className = "activity-item";
        activityElement.innerHTML = `
          <div class="flex items-center gap-3">
            <i class="fas fa-plus-circle" style="color: var(--accent-success);"></i>
            <div>
              <div class="text-sm">${action}</div>
              <div class="text-sm" style="color: var(--text-muted);">${details}</div>
            </div>
          </div>
          <div class="text-sm" style="color: var(--text-muted);">
            ${new Date().toLocaleTimeString("pl-PL", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        `;

        container.insertBefore(activityElement, container.firstChild);

        // Keep only last 10 activities
        while (container.children.length > 10) {
          container.removeChild(container.lastChild);
        }
      }

      // === NOTIFICATION SYSTEM ===
      function showNotification(message, type = "info") {
        // Create notification element
        const notification = document.createElement("div");
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 16px 20px;
          border-radius: 8px;
          color: white;
          font-weight: 500;
          z-index: 1000;
          transform: translateX(100%);
          transition: transform 0.3s ease;
          max-width: 300px;
          box-shadow: var(--shadow-lg);
        `;

        // Set background color based on type
        const colors = {
          success: "var(--accent-success)",
          error: "var(--accent-danger)",
          warning: "var(--accent-warning)",
          info: "var(--accent-secondary)",
        };
        notification.style.background = colors[type] || colors.info;

        notification.innerHTML = `
          <div class="flex items-center gap-2">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
          </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
          notification.style.transform = "translateX(0)";
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
          notification.style.transform = "translateX(100%)";
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }, 5000);
      }

      function getNotificationIcon(type) {
        const icons = {
          success: "check-circle",
          error: "exclamation-circle",
          warning: "exclamation-triangle",
          info: "info-circle",
        };
        return icons[type] || icons.info;
      }

      // === EVENT LISTENERS ===
      function setupEventListeners() {
        // Add click handlers for action buttons
        document.addEventListener("click", function (e) {
          if (e.target.closest(".action-btn")) {
            e.preventDefault();
          }
        });
      }

      // === PLACEHOLDER FUNCTIONS ===
      function refreshData() {
        showNotification("Odświeżanie danych...", "info");
        loadData();
      }

      function filterSignals() {
        // Implementation for filtering signals
        console.log("Filtering signals...");
      }

      function exportData() {
        showNotification("Eksportowanie danych...", "info");
        // Implementation for data export
      }

      function viewSignal(id) {
        showNotification(`Wyświetlanie sygnału ${id}`, "info");
      }

      function editSignal(id) {
        showNotification(`Edycja sygnału ${id}`, "info");
      }

      async function loadPairs() {
        try {
          const response = await fetch("/api/pairs");
          const pairs = await response.json();

          const select = document.getElementById("pairFilter");
          if (select) {
            pairs.forEach((pair) => {
              const option = document.createElement("option");
              option.value = pair;
              option.textContent = pair;
              select.appendChild(option);
            });
          }
        } catch (error) {
          console.error("Error loading pairs:", error);
        }
      }

      // === CHART INITIALIZATION ===
      function initCharts() {
        // Initialize charts with dark theme
        Chart.defaults.color = "var(--text-secondary)";
        Chart.defaults.borderColor = "var(--border-primary)";
        Chart.defaults.backgroundColor = "var(--bg-tertiary)";

        // Initialize empty charts
        initPnlChart();
        initStatusChart();
        initPnlDistributionChart();
        initPairPerformanceChart();
      }

      function initPnlChart() {
        const ctx = document.getElementById("pnlChart");
        if (!ctx) return;

        pnlChart = new Chart(ctx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Cumulative PnL",
                data: [],
                borderColor: "var(--accent-primary)",
                backgroundColor: "rgba(0, 212, 170, 0.1)",
                fill: true,
                tension: 0.4,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                labels: {
                  color: "var(--text-primary)",
                },
              },
            },
            scales: {
              x: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
              y: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
            },
          },
        });
      }

      function initStatusChart() {
        const ctx = document.getElementById("statusChart");
        if (!ctx) return;

        statusChart = new Chart(ctx, {
          type: "doughnut",
          data: {
            labels: ["TP Hit", "SL Hit", "Expired", "Active"],
            datasets: [
              {
                data: [0, 0, 0, 0],
                backgroundColor: [
                  "var(--accent-success)",
                  "var(--accent-danger)",
                  "var(--accent-warning)",
                  "var(--accent-secondary)",
                ],
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: "bottom",
                labels: {
                  color: "var(--text-primary)",
                  padding: 20,
                },
              },
            },
          },
        });
      }

      function initPnlDistributionChart() {
        const ctx = document.getElementById("pnlDistributionChart");
        if (!ctx) return;

        pnlDistributionChart = new Chart(ctx, {
          type: "bar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Frequency",
                data: [],
                backgroundColor: "var(--accent-primary)",
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                labels: {
                  color: "var(--text-primary)",
                },
              },
            },
            scales: {
              x: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
              y: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
            },
          },
        });
      }

      function initPairPerformanceChart() {
        const ctx = document.getElementById("pairPerformanceChart");
        if (!ctx) return;

        pairPerformanceChart = new Chart(ctx, {
          type: "horizontalBar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Total PnL",
                data: [],
                backgroundColor: "var(--accent-secondary)",
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                labels: {
                  color: "var(--text-primary)",
                },
              },
            },
            scales: {
              x: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
              y: {
                ticks: { color: "var(--text-secondary)" },
                grid: { color: "var(--border-primary)" },
              },
            },
          },
        });
      }

      // === CHART DATA LOADING ===
      async function loadPnlChart() {
        try {
          const url = currentTimeFilter
            ? `/api/pnl-chart?days=${currentTimeFilter}`
            : "/api/pnl-chart";

          const response = await fetch(url);
          const data = await response.json();

          if (pnlChart && data.labels && data.values) {
            pnlChart.data.labels = data.labels;
            pnlChart.data.datasets[0].data = data.values;
            pnlChart.update();
          }
        } catch (error) {
          console.error("Error loading PnL chart:", error);
        }
      }

      async function loadPnlDistribution() {
        try {
          const response = await fetch("/api/pnl-distribution");
          const data = await response.json();

          if (pnlDistributionChart && data.labels && data.values) {
            pnlDistributionChart.data.labels = data.labels;
            pnlDistributionChart.data.datasets[0].data = data.values;
            pnlDistributionChart.update();
          }
        } catch (error) {
          console.error("Error loading PnL distribution:", error);
        }
      }

      async function loadPairPerformance() {
        try {
          const response = await fetch("/api/statistics");
          const data = await response.json();

          if (pairPerformanceChart && data.pair_stats) {
            const pairs = data.pair_stats.slice(0, 10); // Top 10 pairs
            pairPerformanceChart.data.labels = pairs.map((p) => p.pair);
            pairPerformanceChart.data.datasets[0].data = pairs.map(
              (p) => (p.total_pnl || 0) * 100
            );
            pairPerformanceChart.update();
          }
        } catch (error) {
          console.error("Error loading pair performance:", error);
        }
      }

      async function loadPerformanceMetrics() {
        try {
          const url = currentTimeFilter
            ? `/api/performance-metrics?days=${currentTimeFilter}`
            : "/api/performance-metrics";

          const response = await fetch(url);
          const metrics = await response.json();

          // Update status chart with real data
          if (statusChart) {
            // This would need to be implemented based on your API
            statusChart.update();
          }
        } catch (error) {
          console.error("Error loading performance metrics:", error);
        }
      }
    </script>
  </body>
</html>
