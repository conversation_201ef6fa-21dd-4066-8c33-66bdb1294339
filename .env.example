# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_server_id_here
DISCORD_CHANNEL_ID=your_discord_channel_id_here

# Bybit API Configuration
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# Monitoring Configuration
PRICE_CHECK_INTERVAL_SEC=60
DB_PATH=signals.db

# Bot Message Handling Configuration
# C<PERSON> zez<PERSON><PERSON> na sygnały od botów (true/false)
ALLOW_BOT_MESSAGES=false

# Lista ID botów dozwolonych do wysyłania sygnałów (oddzielone przecinkami)
# Pozostaw puste aby zezwolić wszystkim botom (gdy ALLOW_BOT_MESSAGES=true)
BOT_WHITELIST=1234567890123456789,9876543210987654321

# Czas ważności sygnału w godzinach (domyślnie 48)
SIGNAL_VALIDITY_HOURS=48
