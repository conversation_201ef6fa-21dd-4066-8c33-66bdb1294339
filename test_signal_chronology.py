#!/usr/bin/env python3
"""
Test chronologii sygnałów - sprawdza czy system poprawnie śledzi
kolejno<PERSON>ć osiągania poziomów entry, TP i SL.

Autor: AI Assistant
Data: 2025-06-14
"""

import sqlite3
import os
from datetime import datetime, timezone, timedelta
from dotenv import load_dotenv

# Ładowanie konfiguracji
load_dotenv()
DB_PATH = os.getenv('DB_PATH', 'signals.db')

def test_signal_chronology():
    """Test chronologii sygnałów."""
    
    print("🧪 Test chronologii sygnałów")
    print("=" * 50)
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Sprawdź strukturę tabeli
    cursor.execute("PRAGMA table_info(signals)")
    columns = [row[1] for row in cursor.fetchall()]
    
    required_columns = ['entry_hit_timestamp', 'entry_hit_price']
    missing_columns = [col for col in required_columns if col not in columns]
    
    if missing_columns:
        print(f"❌ Brakuje kolumn: {', '.join(missing_columns)}")
        print("💡 Uruchom najpierw: python migrate_add_entry_tracking.py")
        return False
    
    print("✅ Wszystkie wymagane kolumny istnieją")
    
    # Test 1: Sprawdź sygnały NEW
    cursor.execute("SELECT COUNT(*) FROM signals WHERE status = 'NEW'")
    new_count = cursor.fetchone()[0]
    print(f"\n📊 Sygnały NEW: {new_count}")
    
    if new_count > 0:
        cursor.execute("""
            SELECT id, pair, side, entry, timestamp 
            FROM signals 
            WHERE status = 'NEW' 
            ORDER BY timestamp DESC 
            LIMIT 3
        """)
        print("   Przykłady sygnałów NEW:")
        for sig_id, pair, side, entry, ts in cursor.fetchall():
            print(f"   - ID {sig_id}: {side} {pair} @ {entry} ({ts})")
    
    # Test 2: Sprawdź sygnały ENTRY_HIT
    cursor.execute("SELECT COUNT(*) FROM signals WHERE status = 'ENTRY_HIT'")
    entry_hit_count = cursor.fetchone()[0]
    print(f"\n📊 Sygnały ENTRY_HIT: {entry_hit_count}")
    
    if entry_hit_count > 0:
        cursor.execute("""
            SELECT id, pair, side, entry, entry_hit_price, entry_hit_timestamp, timestamp
            FROM signals 
            WHERE status = 'ENTRY_HIT' 
            ORDER BY timestamp DESC 
            LIMIT 3
        """)
        print("   Przykłady sygnałów ENTRY_HIT:")
        for sig_id, pair, side, entry, hit_price, hit_ts, orig_ts in cursor.fetchall():
            print(f"   - ID {sig_id}: {side} {pair} @ {entry}")
            print(f"     Entry hit: {hit_price} at {hit_ts}")
            print(f"     Original: {orig_ts}")
    
    # Test 3: Sprawdź chronologię dla zamkniętych sygnałów
    cursor.execute("""
        SELECT COUNT(*) 
        FROM signals 
        WHERE status IN ('TP_HIT', 'SL_HIT') 
        AND entry_hit_timestamp IS NOT NULL
    """)
    closed_with_entry_count = cursor.fetchone()[0]
    print(f"\n📊 Zamknięte sygnały z entry tracking: {closed_with_entry_count}")
    
    # Test 4: Sprawdź potencjalne problemy chronologii
    cursor.execute("""
        SELECT id, pair, status, timestamp, entry_hit_timestamp, close_timestamp
        FROM signals 
        WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
        AND entry_hit_timestamp IS NOT NULL
        AND close_timestamp IS NOT NULL
        AND entry_hit_timestamp > close_timestamp
    """)
    chronology_issues = cursor.fetchall()
    
    if chronology_issues:
        print(f"\n⚠️  Znaleziono {len(chronology_issues)} problemów chronologii:")
        for sig_id, pair, status, orig_ts, entry_ts, close_ts in chronology_issues:
            print(f"   - ID {sig_id} ({pair}): entry_hit_timestamp > close_timestamp")
    else:
        print("\n✅ Brak problemów chronologii")
    
    # Test 5: Sprawdź sygnały bez entry_hit ale ze statusem zamkniętym
    cursor.execute("""
        SELECT COUNT(*)
        FROM signals 
        WHERE status IN ('TP_HIT', 'SL_HIT')
        AND entry_hit_timestamp IS NULL
    """)
    closed_without_entry = cursor.fetchone()[0]
    
    if closed_without_entry > 0:
        print(f"\n⚠️  {closed_without_entry} zamkniętych sygnałów bez entry tracking")
        print("   (prawdopodobnie stare sygnały sprzed implementacji)")
    else:
        print("\n✅ Wszystkie zamknięte sygnały mają entry tracking")
    
    # Test 6: Symulacja sprawdzania chronologii
    print(f"\n🔍 Symulacja logiki chronologii:")
    
    # Przykład: sygnał otrzymany o 10:00, entry hit o 10:05, sprawdzamy TP/SL o 10:10
    now = datetime.now(timezone.utc)
    signal_time = now - timedelta(minutes=10)  # 10 minut temu
    entry_hit_time = now - timedelta(minutes=5)  # 5 minut temu
    
    print(f"   Sygnał otrzymany: {signal_time.strftime('%H:%M:%S')}")
    print(f"   Entry osiągnięte:  {entry_hit_time.strftime('%H:%M:%S')}")
    print(f"   Sprawdzamy teraz:  {now.strftime('%H:%M:%S')}")
    
    # Sprawdź czy teraz > entry_hit_time (warunek do sprawdzania TP/SL)
    can_check_tp_sl = now > entry_hit_time
    print(f"   Można sprawdzać TP/SL: {'✅ TAK' if can_check_tp_sl else '❌ NIE'}")
    
    conn.close()
    
    print(f"\n{'='*50}")
    print("🎯 PODSUMOWANIE TESTU CHRONOLOGII:")
    print(f"   - Sygnały NEW: {new_count}")
    print(f"   - Sygnały ENTRY_HIT: {entry_hit_count}")
    print(f"   - Zamknięte z entry tracking: {closed_with_entry_count}")
    print(f"   - Problemy chronologii: {len(chronology_issues)}")
    print(f"   - Zamknięte bez entry: {closed_without_entry}")
    
    if len(chronology_issues) == 0 and closed_without_entry <= 10:  # Tolerujemy stare sygnały
        print("\n✅ System chronologii działa poprawnie!")
        return True
    else:
        print("\n⚠️  System wymaga uwagi - sprawdź problemy powyżej")
        return False

if __name__ == '__main__':
    try:
        success = test_signal_chronology()
        if success:
            print("\n🚀 System gotowy do monitorowania z chronologią!")
        else:
            print("\n🔧 Wymagane poprawki przed uruchomieniem")
    except Exception as e:
        print(f"\n❌ Błąd podczas testu: {e}")
