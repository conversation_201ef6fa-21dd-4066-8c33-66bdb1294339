#!/usr/bin/env python3
"""
Migracja statusów sygnałów do nowego systemu.

Stare statusy -> Nowe statusy:
- 'open' -> 'NEW' (jeś<PERSON> nie osiągnięto entry) lub 'ENTRY_HIT' (jeś<PERSON> osiągnięto entry)
- 'tp' -> 'TP_HIT'
- 'sl' -> 'SL_HIT'
- 'timeout' -> 'EXPIRED'

Autor: AI Assistant
Data: 2025-06-14
"""

import sqlite3
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Ładowanie konfiguracji
load_dotenv()
DB_PATH = os.getenv('DB_PATH', 'signals.db')

def migrate_signal_statuses():
    """Migruj statusy sygnałów do nowego systemu."""
    
    print("🔄 Rozpoczynam migrację statusów sygnałów...")
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Sprawdź obecne statusy
    cursor.execute('SELECT status, COUNT(*) FROM signals GROUP BY status')
    old_statuses = cursor.fetchall()
    print("\n📊 Obecne statusy w bazie:")
    for status, count in old_statuses:
        print(f"  {status}: {count}")
    
    # Mapowanie starych statusów na nowe
    status_mapping = {
        'tp': 'TP_HIT',
        'sl': 'SL_HIT', 
        'timeout': 'EXPIRED'
    }
    
    # Migruj proste mapowania
    for old_status, new_status in status_mapping.items():
        cursor.execute(
            "UPDATE signals SET status = ? WHERE status = ?",
            (new_status, old_status)
        )
        affected = cursor.rowcount
        if affected > 0:
            print(f"✅ Zaktualizowano {affected} sygnałów: {old_status} -> {new_status}")
    
    # Obsługa statusu 'open' - wymaga sprawdzenia czy osiągnięto entry
    cursor.execute("SELECT COUNT(*) FROM signals WHERE status = 'open'")
    open_count = cursor.fetchone()[0]
    
    if open_count > 0:
        print(f"\n🔍 Analizuję {open_count} sygnałów ze statusem 'open'...")
        
        # Dla uproszczenia, wszystkie 'open' sygnały ustawiamy jako 'NEW'
        # W rzeczywistości należałoby sprawdzić historyczne ceny
        cursor.execute(
            "UPDATE signals SET status = 'NEW' WHERE status = 'open'"
        )
        print(f"✅ Zaktualizowano {open_count} sygnałów: open -> NEW")
    
    # Obsługa statusu 'historical' - pozostawiamy bez zmian
    cursor.execute("SELECT COUNT(*) FROM signals WHERE status = 'historical'")
    historical_count = cursor.fetchone()[0]
    if historical_count > 0:
        print(f"ℹ️  Pozostawiono {historical_count} sygnałów historycznych bez zmian")
    
    conn.commit()
    
    # Sprawdź nowe statusy
    cursor.execute('SELECT status, COUNT(*) FROM signals GROUP BY status')
    new_statuses = cursor.fetchall()
    print("\n📊 Statusy po migracji:")
    for status, count in new_statuses:
        print(f"  {status}: {count}")
    
    conn.close()
    print("\n✅ Migracja zakończona pomyślnie!")

def verify_migration():
    """Sprawdź poprawność migracji."""
    
    print("\n🔍 Weryfikacja migracji...")
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Sprawdź czy nie ma starych statusów
    old_statuses = ['open', 'tp', 'sl', 'timeout']
    for status in old_statuses:
        cursor.execute("SELECT COUNT(*) FROM signals WHERE status = ?", (status,))
        count = cursor.fetchone()[0]
        if count > 0:
            print(f"⚠️  Znaleziono {count} sygnałów ze starym statusem: {status}")
        else:
            print(f"✅ Brak sygnałów ze statusem: {status}")
    
    # Sprawdź nowe statusy
    new_statuses = ['NEW', 'ENTRY_HIT', 'TP_HIT', 'SL_HIT', 'EXPIRED']
    for status in new_statuses:
        cursor.execute("SELECT COUNT(*) FROM signals WHERE status = ?", (status,))
        count = cursor.fetchone()[0]
        print(f"📊 {status}: {count} sygnałów")
    
    conn.close()

if __name__ == '__main__':
    try:
        migrate_signal_statuses()
        verify_migration()
        print("\n🎉 Migracja zakończona pomyślnie!")
        print("💡 Możesz teraz uruchomić system z nowymi statusami.")
    except Exception as e:
        print(f"\n❌ Błąd podczas migracji: {e}")
        print("🔧 Sprawdź bazę danych i spróbuj ponownie.")
