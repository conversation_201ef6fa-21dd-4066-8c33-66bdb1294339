#!/usr/bin/env python3
"""
Sprawdź rzeczywiste sygnały z bazy danych i ich timeframe.
"""

import sqlite3
import re
import os
from dotenv import load_dotenv

# Ładowanie zmiennych środowiskowych
load_dotenv()
SIGNAL_VALIDITY_HOURS = int(os.getenv('SIGNAL_VALIDITY_HOURS', 48))

def check_real_signals():
    """Sprawdź rzeczywiste sygnały z bazy danych."""
    
    conn = sqlite3.connect('signals.db')
    cursor = conn.cursor()
    
    # Pobierz kilka sygnałów z timeframe = 1
    cursor.execute('''
        SELECT message_id, pair, side, entry, tp, sl, timeframe_min, status
        FROM signals 
        WHERE timeframe_min = 1
        ORDER BY timestamp DESC 
        LIMIT 10
    ''')
    
    signals = cursor.fetchall()
    print(f"Znaleziono {len(signals)} sygnałów z TF=1 minuta:")
    print("="*60)
    
    for signal in signals:
        msg_id, pair, side, entry, tp, sl, tf, status = signal
        print(f"ID: {msg_id}")
        print(f"Signal: {side} {pair} @ {entry} TP {tp} SL {sl}")
        print(f"TF: {tf}m, Status: {status}")
        print("-" * 40)
    
    # Sprawdź czy są sygnały z innym timeframe
    cursor.execute('''
        SELECT DISTINCT timeframe_min, COUNT(*) 
        FROM signals 
        GROUP BY timeframe_min
        ORDER BY timeframe_min
    ''')
    
    tf_stats = cursor.fetchall()
    print(f"\nStatystyki timeframe:")
    for tf, count in tf_stats:
        print(f"  {tf} minut: {count} sygnałów")
    
    conn.close()

def analyze_timeframe_issue():
    """Analizuj problem z timeframe."""
    
    print(f"\nAnaliza problemu timeframe:")
    print(f"SIGNAL_VALIDITY_HOURS z .env: {SIGNAL_VALIDITY_HOURS}")
    print(f"Domyślny timeframe: {SIGNAL_VALIDITY_HOURS * 60} minut")
    
    # Sprawdź czy sygnały zawierają informację o timeframe
    print(f"\nPrawdopodobne przyczyny problemu:")
    print(f"1. Sygnały z Discord zawierają 'TF 1' lub podobne")
    print(f"2. Regex pattern łapie liczbę '1' jako timeframe")
    print(f"3. Kod używa tej wartości zamiast domyślnej")
    
    # Sugestie rozwiązania
    print(f"\nMożliwe rozwiązania:")
    print(f"1. Ignorować timeframe z sygnału i używać tylko domyślnego")
    print(f"2. Dodać walidację minimalnego timeframe (np. min 15 minut)")
    print(f"3. Interpretować TF jako godziny zamiast minut")
    print(f"4. Dodać mapowanie: 1->60min, 5->300min, etc.")

if __name__ == '__main__':
    check_real_signals()
    analyze_timeframe_issue()
