#!/usr/bin/env python3
"""
Migra<PERSON><PERSON> bazy danych - dodanie kolumn do śledzenia osiągnięcia poziomu entry.

Dodaje kolumny:
- entry_hit_timestamp: kiedy poziom entry został osiągnięty
- entry_hit_price: po jakiej cenie poziom entry został osiągnięty

Autor: AI Assistant
Data: 2025-06-14
"""

import sqlite3
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Ładowanie konfiguracji
load_dotenv()
DB_PATH = os.getenv('DB_PATH', 'signals.db')

def migrate_add_entry_tracking():
    """Dodaj kolumny do śledzenia osiągnięcia poziomu entry."""
    
    print("🔄 Rozpoczynam migrację - dodawanie kolumn entry tracking...")
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Sprawdź obecną strukturę tabeli
    cursor.execute("PRAGMA table_info(signals)")
    columns = [row[1] for row in cursor.fetchall()]
    print(f"📊 Obecne kolumny: {', '.join(columns)}")
    
    # Dodaj nowe kolumny jeśli nie istnieją
    new_columns_added = []
    
    if 'entry_hit_timestamp' not in columns:
        try:
            cursor.execute('ALTER TABLE signals ADD COLUMN entry_hit_timestamp DATETIME')
            new_columns_added.append('entry_hit_timestamp')
            print("✅ Dodano kolumnę: entry_hit_timestamp")
        except sqlite3.OperationalError as e:
            print(f"❌ Błąd dodawania entry_hit_timestamp: {e}")
    else:
        print("ℹ️  Kolumna entry_hit_timestamp już istnieje")
    
    if 'entry_hit_price' not in columns:
        try:
            cursor.execute('ALTER TABLE signals ADD COLUMN entry_hit_price REAL')
            new_columns_added.append('entry_hit_price')
            print("✅ Dodano kolumnę: entry_hit_price")
        except sqlite3.OperationalError as e:
            print(f"❌ Błąd dodawania entry_hit_price: {e}")
    else:
        print("ℹ️  Kolumna entry_hit_price już istnieje")
    
    if new_columns_added:
        conn.commit()
        print(f"✅ Dodano {len(new_columns_added)} nowych kolumn")
        
        # Aktualizuj istniejące sygnały ze statusem ENTRY_HIT
        cursor.execute("SELECT COUNT(*) FROM signals WHERE status = 'ENTRY_HIT'")
        entry_hit_count = cursor.fetchone()[0]
        
        if entry_hit_count > 0:
            print(f"🔍 Znaleziono {entry_hit_count} sygnałów ze statusem ENTRY_HIT")
            print("💡 Dla tych sygnałów ustawię entry_hit_timestamp na timestamp sygnału")
            print("   (przybliżenie, ponieważ nie mamy dokładnych danych historycznych)")
            
            # Ustaw entry_hit_timestamp na timestamp sygnału dla istniejących ENTRY_HIT
            cursor.execute("""
                UPDATE signals 
                SET entry_hit_timestamp = timestamp,
                    entry_hit_price = entry
                WHERE status = 'ENTRY_HIT' 
                AND entry_hit_timestamp IS NULL
            """)
            updated = cursor.rowcount
            conn.commit()
            print(f"✅ Zaktualizowano {updated} sygnałów ENTRY_HIT")
    else:
        print("ℹ️  Wszystkie kolumny już istnieją - brak zmian")
    
    # Sprawdź finalną strukturę
    cursor.execute("PRAGMA table_info(signals)")
    final_columns = [row[1] for row in cursor.fetchall()]
    print(f"\n📊 Finalne kolumny: {', '.join(final_columns)}")
    
    # Sprawdź statystyki
    cursor.execute("SELECT status, COUNT(*) FROM signals GROUP BY status")
    print("\n📈 Statystyki statusów:")
    for status, count in cursor.fetchall():
        print(f"  {status}: {count}")
    
    # Sprawdź sygnały z entry_hit_timestamp
    cursor.execute("SELECT COUNT(*) FROM signals WHERE entry_hit_timestamp IS NOT NULL")
    entry_tracked_count = cursor.fetchone()[0]
    print(f"\n🎯 Sygnały z entry tracking: {entry_tracked_count}")
    
    conn.close()
    print("\n✅ Migracja zakończona pomyślnie!")

if __name__ == '__main__':
    try:
        migrate_add_entry_tracking()
        print("\n🎉 Baza danych gotowa do śledzenia chronologii sygnałów!")
        print("💡 Teraz system będzie sprawdzał czy poziomy zostały osiągnięte PO otrzymaniu sygnału.")
    except Exception as e:
        print(f"\n❌ Błąd podczas migracji: {e}")
        print("🔧 Sprawdź bazę danych i spróbuj ponownie.")
