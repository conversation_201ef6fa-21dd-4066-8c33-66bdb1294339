#!/usr/bin/env python3
"""
Dodaj przykładowe sygnały do bazy danych dla testowania dashboard.

Autor: AI Assistant
Data: 2025-06-14
"""

import sqlite3
import random
from datetime import datetime, timedelta, timezone

DB_PATH = 'signals.db'

def add_sample_signals():
    """Dodaj przykładowe sygnały do bazy danych."""

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Przykładowe pary
    pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
    sides = ['BUY', 'SELL']
    statuses = ['TP_HIT', 'SL_HIT', 'EXPIRED', 'NEW', 'ENTRY_HIT']

    sample_signals = []

    # Generuj 50 przykładowych sygnałów
    for i in range(50):
        pair = random.choice(pairs)
        side = random.choice(sides)

        # Losowe ceny
        if pair == 'BTCUSDT':
            entry = random.uniform(40000, 50000)
        elif pair == 'ETHUSDT':
            entry = random.uniform(2000, 3000)
        elif pair == 'ADAUSDT':
            entry = random.uniform(0.3, 0.8)
        elif pair == 'SOLUSDT':
            entry = random.uniform(80, 150)
        else:  # DOTUSDT
            entry = random.uniform(5, 15)

        # TP i SL na podstawie kierunku
        if side == 'BUY':
            tp = entry * random.uniform(1.02, 1.08)  # 2-8% zysk
            sl = entry * random.uniform(0.92, 0.98)  # 2-8% strata
        else:  # SELL
            tp = entry * random.uniform(0.92, 0.98)  # 2-8% zysk
            sl = entry * random.uniform(1.02, 1.08)  # 2-8% strata

        # Losowy timeframe
        timeframe_min = random.choice([30, 60, 120, 240])

        # Losowy timestamp (ostatnie 30 dni)
        timestamp = datetime.now(timezone.utc) - timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )

        # Status i wyniki
        status = random.choice(statuses)
        close_timestamp = None
        exit_price = None
        pnl = None

        if status not in ['NEW', 'ENTRY_HIT']:
            # Zamknięty sygnał
            close_timestamp = timestamp + timedelta(minutes=random.randint(10, timeframe_min))

            if status == 'TP_HIT':
                exit_price = tp
            elif status == 'SL_HIT':
                exit_price = sl
            else:  # EXPIRED
                # Losowa cena między entry a TP/SL
                if side == 'BUY':
                    exit_price = random.uniform(sl, tp)
                else:
                    exit_price = random.uniform(tp, sl)

            # Oblicz PnL
            pnl = (exit_price - entry) / entry * (1 if side == 'BUY' else -1)

        signal = {
            'message_id': f'sample_{i+1000}',
            'pair': pair,
            'side': side,
            'entry': round(entry, 2),
            'tp': round(tp, 2),
            'sl': round(sl, 2),
            'timestamp': timestamp,
            'timeframe_min': timeframe_min,
            'status': status,
            'close_timestamp': close_timestamp,
            'exit_price': round(exit_price, 2) if exit_price else None,
            'pnl': round(pnl, 4) if pnl else None
        }

        sample_signals.append(signal)

    # Wstaw sygnały do bazy
    for signal in sample_signals:
        try:
            cursor.execute("""
                INSERT INTO signals (
                    message_id, pair, side, entry, tp, sl,
                    timestamp, timeframe_min, status, close_timestamp,
                    exit_price, pnl
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal['message_id'],
                signal['pair'],
                signal['side'],
                signal['entry'],
                signal['tp'],
                signal['sl'],
                signal['timestamp'],
                signal['timeframe_min'],
                signal['status'],
                signal['close_timestamp'],
                signal['exit_price'],
                signal['pnl']
            ))
            print(f"✅ Dodano: {signal['side']} {signal['pair']} @ {signal['entry']} (status: {signal['status']})")
        except sqlite3.IntegrityError:
            print(f"⚠️  Sygnał {signal['message_id']} już istnieje")

    conn.commit()
    conn.close()

    print(f"\n🎉 Dodano {len(sample_signals)} przykładowych sygnałów!")

def show_statistics():
    """Pokaż statystyki bazy danych."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("SELECT COUNT(*) FROM signals")
    total = cursor.fetchone()[0]

    cursor.execute("SELECT status, COUNT(*) FROM signals GROUP BY status")
    status_counts = cursor.fetchall()

    cursor.execute("SELECT AVG(pnl) FROM signals WHERE pnl IS NOT NULL")
    avg_pnl = cursor.fetchone()[0] or 0

    conn.close()

    print(f"\n📊 Statystyki bazy danych:")
    print(f"   Wszystkie sygnały: {total}")
    for status, count in status_counts:
        print(f"   {status}: {count}")
    print(f"   Średni PnL: {avg_pnl:.2%}")

if __name__ == '__main__':
    print("🎲 Generator przykładowych sygnałów")
    print("=" * 40)

    # Sprawdź czy baza istnieje
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM signals")
        existing_count = cursor.fetchone()[0]
        conn.close()

        print(f"📊 Aktualna liczba sygnałów w bazie: {existing_count}")

        if existing_count > 0:
            response = input("Czy dodać więcej przykładowych sygnałów? (y/n): ").lower()
            if response != 'y':
                show_statistics()
                exit()

        add_sample_signals()
        show_statistics()

    except Exception as e:
        print(f"❌ Błąd: {e}")
        print("Upewnij się, że baza danych istnieje (uruchom najpierw główny bot)")
